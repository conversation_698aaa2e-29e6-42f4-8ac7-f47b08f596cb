import { useColorScheme } from 'react-native';

interface DesignTokens {
  colors: {
    primary: {
      main: string;
      light: string;
      dark: string;
      contrast: string;
    };
    secondary: {
      main: string;
      light: string;
      dark: string;
      contrast: string;
    };
    background: {
      primary: string;
      secondary: string;
      tertiary: string;
    };
    surface: {
      primary: string;
      secondary: string;
      elevated: string;
      card: string;
    };
    text: {
      primary: string;
      secondary: string;
      tertiary: string;
      onPrimary: string;
      onSecondary: string;
      disabled: string;
    };
    border: {
      primary: string;
      secondary: string;
      focus: string;
    };
    status: {
      success: string;
      warning: string;
      error: string;
      info: string;
    };
  };
  typography: {
    fontFamilies: {
      primary: string;
      secondary: string;
    };
    fontSizes: {
      xs: number;
      sm: number;
      base: number;
      lg: number;
      xl: number;
      '2xl': number;
      '3xl': number;
      '4xl': number;
    };
    fontWeights: {
      light: number;
      regular: number;
      medium: number;
      semibold: number;
      bold: number;
    };
    lineHeights: {
      tight: number;
      normal: number;
      relaxed: number;
      loose: number;
    };
    textStyles: {
      h1: {
        fontSize: number;
        fontWeight: number;
        lineHeight: number;
      };
      h2: {
        fontSize: number;
        fontWeight: number;
        lineHeight: number;
      };
      h3: {
        fontSize: number;
        fontWeight: number;
        lineHeight: number;
      };
      body: {
        fontSize: number;
        fontWeight: number;
        lineHeight: number;
      };
      bodySmall: {
        fontSize: number;
        fontWeight: number;
        lineHeight: number;
      };
      caption: {
        fontSize: number;
        fontWeight: number;
        lineHeight: number;
      };
      button: {
        fontSize: number;
        fontWeight: number;
        lineHeight: number;
      };
    };
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    '2xl': number;
    '3xl': number;
  };
  borderRadius: {
    none: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    '2xl': number;
    full: number;
  };
  shadows: {
    none: string;
    sm: object;
    md: object;
    lg: object;
    xl: object;
  };
  components: {
    statusBar: {
      height: number;
      backgroundColor: string;
    };
    navigationBar: {
      height: number;
    };
    searchBar: {
      height: number;
      borderRadius: number;
    };
    card: {
      borderRadius: number;
      padding: number;
    };
    button: {
      primary: {
        borderRadius: number;
        paddingVertical: number;
        paddingHorizontal: number;
        fontSize: number;
        fontWeight: number;
      };
      secondary: {
        borderRadius: number;
        paddingVertical: number;
        paddingHorizontal: number;
        borderWidth: number;
      };
      ghost: {
        borderRadius: number;
        paddingVertical: number;
        paddingHorizontal: number;
      };
    };
    input: {
      borderRadius: number;
      paddingVertical: number;
      paddingHorizontal: number;
      fontSize: number;
    };
    avatar: {
      sizes: {
        sm: number;
        md: number;
        lg: number;
        xl: number;
        '2xl': number;
      };
      borderRadius: number;
    };
    badge: {
      borderRadius: number;
      paddingVertical: number;
      paddingHorizontal: number;
      fontSize: number;
      fontWeight: number;
    };
    tabBar: {
      height: number;
      iconSize: number;
    };
    dateChip: {
      borderRadius: number;
      paddingVertical: number;
      paddingHorizontal: number;
      fontSize: number;
    };
  };
  layout: {
    screenPadding: number;
    sectionSpacing: number;
    gridGap: number;
    maxWidth: number;
  };
  animation: {
    duration: {
      fast: number;
      normal: number;
      slow: number;
    };
    easing: {
      ease: string;
      easeIn: string;
      easeOut: string;
      easeInOut: string;
    };
  };
}

const lightTokens: DesignTokens = {
  colors: {
    primary: {
      main: '#262755',
      light: '#3d3d6b',
      dark: '#1a1a3c',
      contrast: '#ffffff',
    },
    secondary: {
      main: '#F6BF1A',
      light: '#f7c940',
      dark: '#d4a312',
      contrast: '#000000',
    },
    background: {
      primary: '#ffffff',
      secondary: '#f8f9fa',
      tertiary: '#f1f3f4',
    },
    surface: {
      primary: '#ffffff',
      secondary: '#f8f9fa',
      elevated: '#ffffff',
      card: '#ffffff',
    },
    text: {
      primary: '#1a1a1a',
      secondary: '#6b7280',
      tertiary: '#9ca3af',
      onPrimary: '#ffffff',
      onSecondary: '#000000',
      disabled: '#d1d5db',
    },
    border: {
      primary: '#e5e7eb',
      secondary: '#f3f4f6',
      focus: '#262755',
    },
    status: {
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6',
    },
  },
  typography: {
    fontFamilies: {
      primary: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
      secondary: 'SF Pro Display, Roboto, sans-serif',
    },
    fontSizes: {
      xs: 12,
      sm: 14,
      base: 16,
      lg: 18,
      xl: 20,
      '2xl': 24,
      '3xl': 30,
      '4xl': 36,
    },
    fontWeights: {
      light: 300,
      regular: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
    lineHeights: {
      tight: 1.2,
      normal: 1.4,
      relaxed: 1.6,
      loose: 1.8,
    },
    textStyles: {
      h1: {
        fontSize: 30,
        fontWeight: 700,
        lineHeight: 1.2,
      },
      h2: {
        fontSize: 24,
        fontWeight: 600,
        lineHeight: 1.3,
      },
      h3: {
        fontSize: 20,
        fontWeight: 600,
        lineHeight: 1.4,
      },
      body: {
        fontSize: 16,
        fontWeight: 400,
        lineHeight: 1.5,
      },
      bodySmall: {
        fontSize: 14,
        fontWeight: 400,
        lineHeight: 1.4,
      },
      caption: {
        fontSize: 12,
        fontWeight: 400,
        lineHeight: 1.3,
      },
      button: {
        fontSize: 16,
        fontWeight: 500,
        lineHeight: 1.2,
      },
    },
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    '2xl': 48,
    '3xl': 64,
  },
  borderRadius: {
    none: 0,
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    '2xl': 24,
    full: 9999,
  },
  shadows: {
    none: 'none',
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.1,
      shadowRadius: 6,
      elevation: 3,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 10 },
      shadowOpacity: 0.1,
      shadowRadius: 15,
      elevation: 5,
    },
    xl: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 20 },
      shadowOpacity: 0.1,
      shadowRadius: 25,
      elevation: 8,
    },
  },
  components: {
    statusBar: {
      height: 44,
      backgroundColor: 'transparent',
    },
    navigationBar: {
      height: 56,
    },
    searchBar: {
      height: 40,
      borderRadius: 12,
    },
    card: {
      borderRadius: 12,
      padding: 16,
    },
    button: {
      primary: {
        borderRadius: 8,
        paddingVertical: 12,
        paddingHorizontal: 24,
        fontSize: 16,
        fontWeight: 500,
      },
      secondary: {
        borderRadius: 8,
        paddingVertical: 12,
        paddingHorizontal: 24,
        borderWidth: 1,
      },
      ghost: {
        borderRadius: 8,
        paddingVertical: 12,
        paddingHorizontal: 24,
      },
    },
    input: {
      borderRadius: 8,
      paddingVertical: 12,
      paddingHorizontal: 16,
      fontSize: 16,
    },
    avatar: {
      sizes: {
        sm: 32,
        md: 40,
        lg: 48,
        xl: 64,
        '2xl': 80,
      },
      borderRadius: 9999,
    },
    badge: {
      borderRadius: 9999,
      paddingVertical: 4,
      paddingHorizontal: 8,
      fontSize: 12,
      fontWeight: 500,
    },
    tabBar: {
      height: 80,
      iconSize: 24,
    },
    dateChip: {
      borderRadius: 9999,
      paddingVertical: 8,
      paddingHorizontal: 12,
      fontSize: 14,
    },
  },
  layout: {
    screenPadding: 16,
    sectionSpacing: 24,
    gridGap: 16,
    maxWidth: 428,
  },
  animation: {
    duration: {
      fast: 150,
      normal: 250,
      slow: 350,
    },
    easing: {
      ease: 'cubic-bezier(0.4, 0, 0.2, 1)',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    },
  },
};

const darkTokens: DesignTokens = {
  ...lightTokens,
  colors: {
    primary: {
      main: '#4c4f82',
      light: '#6366a3',
      dark: '#262755',
      contrast: '#ffffff',
    },
    secondary: {
      main: '#F6BF1A',
      light: '#f7c940',
      dark: '#d4a312',
      contrast: '#000000',
    },
    background: {
      primary: '#0f0f0f',
      secondary: '#1a1a1a',
      tertiary: '#262626',
    },
    surface: {
      primary: '#1a1a1a',
      secondary: '#262626',
      elevated: '#2d2d2d',
      card: '#1f1f1f',
    },
    text: {
      primary: '#ffffff',
      secondary: '#d1d5db',
      tertiary: '#9ca3af',
      onPrimary: '#ffffff',
      onSecondary: '#000000',
      disabled: '#6b7280',
    },
    border: {
      primary: '#374151',
      secondary: '#2d3748',
      focus: '#4c4f82',
    },
    status: {
      success: '#34d399',
      warning: '#fbbf24',
      error: '#f87171',
      info: '#60a5fa',
    },
  },
};

export const useDesignTokens = (): DesignTokens => {
  const colorScheme = useColorScheme();
  return colorScheme === 'dark' ? darkTokens : lightTokens;
};

export const getDesignTokens = (isDark: boolean = false): DesignTokens => {
  return isDark ? darkTokens : lightTokens;
};

export { lightTokens, darkTokens };
export type { DesignTokens };