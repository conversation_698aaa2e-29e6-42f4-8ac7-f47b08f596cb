/**
 * Color constants using design tokens for consistent theming across the app.
 * This file is deprecated in favor of DesignTokens.ts but maintained for backwards compatibility.
 * Use useDesignTokens() hook for new components.
 */

import { useDesignTokens, getDesignTokens } from './DesignTokens';

export const AppColors = {
  // Primary Colors - Updated to match design tokens
  primary: '#262755',
  primaryLight: '#3d3d6b',
  primaryDark: '#1a1a3c',
  
  // Secondary Colors - Updated to match design tokens
  secondary: '#F6BF1A',
  secondaryLight: '#f7c940',
  secondaryDark: '#d4a312',
  
  // Neutral Colors - Updated to match design tokens
  background: '#ffffff',
  surface: '#ffffff',
  surfaceVariant: '#f8f9fa',
  
  // Semantic Colors - Updated to match design tokens
  error: '#ef4444',
  success: '#10b981',
  warning: '#f59e0b',
  info: '#3b82f6',
  
  // Text Colors - Updated to match design tokens
  textPrimary: '#1a1a1a',
  textSecondary: '#6b7280',
  textHint: '#9ca3af',
  textOnPrimary: '#ffffff',
  
  // Chat Colors - Using design token colors
  chatBubbleOwn: '#262755',
  chatBubbleOther: '#f8f9fa',
  chatBubbleSystem: '#f7c940',
  
  // Status Colors - Updated to match design tokens
  online: '#10b981',
  offline: '#9ca3af',
  away: '#f59e0b',
} as const;

export const AppFonts = {
  h1: {
    fontFamily: 'Montserrat-Bold',
    fontSize: 30,
    lineHeight: 36,
    fontWeight: '700' as const,
  },
  h2: {
    fontFamily: 'Montserrat-Bold',
    fontSize: 24,
    lineHeight: 31,
    fontWeight: '600' as const,
  },
  h3: {
    fontFamily: 'Montserrat-SemiBold',
    fontSize: 20,
    lineHeight: 28,
    fontWeight: '600' as const,
  },
  bodyLarge: {
    fontFamily: 'Poppins-Regular',
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '400' as const,
  },
  bodyMedium: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '400' as const,
  },
  caption: {
    fontFamily: 'Poppins-Light',
    fontSize: 12,
    lineHeight: 16,
    fontWeight: '400' as const,
  },
} as const;

export const Colors = {
  light: {
    text: AppColors.textPrimary,
    background: AppColors.background,
    tint: AppColors.primary,
    icon: AppColors.textSecondary,
    tabIconDefault: AppColors.textSecondary,
    tabIconSelected: AppColors.primary,
  },
  dark: {
    text: '#ffffff',
    background: '#0f0f0f',
    tint: '#4c4f82',
    icon: '#d1d5db',
    tabIconDefault: '#d1d5db',
    tabIconSelected: '#4c4f82',
  },
};

// Export design tokens hook for easy access
export { useDesignTokens, getDesignTokens };
