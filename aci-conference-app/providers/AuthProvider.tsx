import React, { createContext, useContext, useEffect, useState } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';

type AuthContextType = {
  session: Session | null;
  user: User | null;
  isLoading: boolean;
  signOut: () => void;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    console.log("AuthProvider: useEffect - Initializing session fetch");
    const fetchSession = async () => {
      try {
        console.log("AuthProvider: fetchSession - Calling getSession()");
        const { data: { session } } = await supabase.auth.getSession();
        console.log("AuthProvider: fetchSession - getSession() resolved. Session:", session);
        setSession(session);
        setUser(session?.user ?? null);
      } catch (error) {
        console.error("AuthProvider: Error fetching session:", error);
      } finally {
        console.log("AuthProvider: fetchSession - Setting isLoading to false");
        setIsLoading(false);
      }
    };

    fetchSession();

    console.log("AuthProvider: Setting up onAuthStateChange listener");
    const { data: authListener } = supabase.auth.onAuthStateChange((_event, session) => {
      console.log("AuthProvider: onAuthStateChange fired. Event:", _event, "Session:", session);
      setSession(session);
      setUser(session?.user ?? null);
    });

    return () => {
      console.log("AuthProvider: Cleaning up onAuthStateChange listener");
      authListener.subscription.unsubscribe();
    };
  }, []);

  const deactivatePushToken = async (userId: string) => {
    try {
      const { error } = await supabase
        .from('push_tokens')
        .update({ is_active: false })
        .eq('user_id', userId);

      if (error) {
        console.error('Error deactivating push token:', error.message);
      }
    } catch (error: any) {
      console.error('Error in deactivatePushToken:', error.message);
    }
  };

  const signOut = async () => {
    if (user?.id) {
      await deactivatePushToken(user.id);
    }
    await supabase.auth.signOut();
  };

  const value = {
    session,
    user,
    isLoading,
    signOut,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
