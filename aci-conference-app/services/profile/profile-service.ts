import { supabaseBase } from '@/lib/supabase';
import { UserProfile, Organization, OrganizationType } from '@/types/profile';

export const getProfile = async (userId: string): Promise<UserProfile | null> => {
  const { data, error } = await supabaseBase
    .from('profiles')
    .select(
      `
      *,
      organization:organizations(*)
      `
    )
    .eq('id', userId)
    .single();

  if (error) {
    throw error;
  }
  return data as UserProfile;
};

export const updateProfile = async (userId: string, updates: Partial<UserProfile>): Promise<UserProfile> => {
  const { data, error } = await supabaseBase
    .from('profiles')
    .update(updates)
    .eq('id', userId)
    .select(
      `
      *,
      organization:organizations(*)
      `
    )
    .single();

  if (error) {
    throw error;
  }
  return data as UserProfile;
};

export const getOrganizations = async (): Promise<Organization[]> => {
  const { data, error } = await supabaseBase
    .from('organizations')
    .select('*');

  if (error) {
    throw error;
  }
  return data as Organization[];
};

export const getOrganizationTypes = async (): Promise<OrganizationType[]> => {
  const { data, error } = await supabaseBase
    .from('organization_types')
    .select('*');

  if (error) {
    throw error;
  }
  return data as OrganizationType[];
};
