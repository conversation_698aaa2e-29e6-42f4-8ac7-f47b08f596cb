import * as Notifications from 'expo-notifications';
import { scheduleEventReminder, scheduleCustomReminder, cancelAllScheduledNotifications, scheduleBreakEndReminder, scheduleDayStartReminder, scheduleCheckInReminder } from '../notificationService';

jest.mock('expo-notifications', () => ({
  scheduleNotificationAsync: jest.fn(),
  cancelAllScheduledNotificationsAsync: jest.fn(),
}));

describe('Notification Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should schedule an event reminder', async () => {
    const event = {
      id: '123',
      title: 'Test Event',
      start_time: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours in the future
      location: 'Test Location',
    };
    await scheduleEventReminder(event, 15);
    expect(Notifications.scheduleNotificationAsync).toHaveBeenCalled();
  });

  it('should schedule a custom notification', async () => {
    const date = new Date(Date.now() + 60 * 1000); // 1 minute in the future
    await scheduleCustomReminder('Test Title', 'Test Body', date);
    expect(Notifications.scheduleNotificationAsync).toHaveBeenCalledWith({
      content: {
        title: 'Test Title',
        body: 'Test Body',
        data: undefined,
      },
      trigger: date,
    });
  });

  it('should cancel all scheduled notifications', async () => {
    await cancelAllScheduledNotifications();
    expect(Notifications.cancelAllScheduledNotificationsAsync).toHaveBeenCalled();
  });

  it('should schedule a break end reminder', async () => {
    await scheduleBreakEndReminder('Next Event', 'Next Location', 5);
    expect(Notifications.scheduleNotificationAsync).toHaveBeenCalled();
  });

  it('should schedule a day start reminder', async () => {
    const date = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours in the future
    await scheduleDayStartReminder(date, 1);
    expect(Notifications.scheduleNotificationAsync).toHaveBeenCalled();
  });

  it('should schedule a check-in reminder', async () => {
    const date = new Date(Date.now() + 60 * 60 * 1000); // 1 hour in the future
    await scheduleCheckInReminder(date);
    expect(Notifications.scheduleNotificationAsync).toHaveBeenCalled();
  });
});