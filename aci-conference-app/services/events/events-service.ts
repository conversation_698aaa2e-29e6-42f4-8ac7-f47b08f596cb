import { supabaseBase } from '@/lib/supabase';
import { Event } from '@/types/events';

interface GetEventsFilters {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  startDate?: Date;
  endDate?: Date;
}

export const getEvents = async (filters?: GetEventsFilters): Promise<Event[]> => {
  let query = supabaseBase
    .from('events')
    .select(
      `
      *,
      speakers:event_speakers(
        speaker:speakers(*)
      ),
      user_favorites!left(user_id)
      `
    )
    .order('start_time', { ascending: true });

  if (filters?.page && filters?.limit) {
    query = query.range((filters.page - 1) * filters.limit, filters.page * filters.limit - 1);
  }

  if (filters?.search) {
    query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
  }

  if (filters?.category) {
    query = query.eq('category', filters.category);
  }

  if (filters?.startDate) {
    query = query.gte('start_time', filters.startDate.toISOString());
  }

  if (filters?.endDate) {
    query = query.lte('end_time', filters.endDate.toISOString());
  }

  const { data, error } = await query;

  if (error) {
    throw error;
  }

  return data as Event[];
};

export const getUniqueEventDates = async (): Promise<Date[]> => {
  const { data, error } = await supabaseBase
    .from('events')
    .select('start_time')
    .order('start_time', { ascending: true });

  if (error) {
    throw error;
  }

  // Extract unique dates and convert to Date objects
  const uniqueDates = Array.from(new Set(data.map(event => new Date(event.start_time).toISOString().split('T')[0])))
    .map(dateString => new Date(dateString))
    .sort((a, b) => a.getTime() - b.getTime());

  return uniqueDates;
};

export const getEventById = async (id: string): Promise<Event | null> => {
  const { data, error } = await supabaseBase
    .from('events')
    .select(`
      *,
      speakers:event_speakers(
        speaker:speakers(*)
      ),
      user_favorites!left(user_id)
    `)
    .eq('id', id)
    .single();

  if (error) {
    throw error;
  }

  return data as Event;
};
