import 'react-native-url-polyfill/auto';
import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { SplashScreen, Stack, useRouter, useSegments } from 'expo-router';
import { AuthProvider, useAuth } from '../providers/AuthProvider';
import { supabaseBase } from '../lib/supabase';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useEffect, useRef } from 'react';
import { Platform } from 'react-native';
import * as Notifications from 'expo-notifications';

import { StatusBar } from 'expo-status-bar';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

const queryClient = new QueryClient();

async function registerForPushNotificationsAsync(userId: string) {
  let token;

  if (Platform.OS === 'android') {
    Notifications.setNotificationChannelGroupAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  const { status: existingStatus } = await Notifications.getPermissionsAsync();
  let finalStatus = existingStatus;
  if (existingStatus !== 'granted') {
    const { status } = await Notifications.requestPermissionsAsync();
    finalStatus = status;
  }
  if (finalStatus !== 'granted') {
    alert('Failed to get push token for push notification!');
    return;
  }
  token = (await Notifications.getExpoPushTokenAsync()).data;
  console.log(token);

  // Send the token to your Supabase Edge Function
  try {
    const { data, error } = await supabaseBase
      .from('push_tokens')
      .upsert({
        user_id: userId,
        expo_push_token: token,
        device_type: Platform.OS,
      }, { onConflict: 'expo_push_token' });

    if (error) {
      console.error('Error saving push token to Supabase:', error);
    } else {
      console.log('Push token saved to Supabase:', data);
    }
  } catch (error: any) {
    console.error('Error in Supabase operation:', error.message);
  }

  return token;
}

function RootLayoutNav({ loaded }: { loaded: boolean }) {
  const { session, isLoading } = useAuth();
  const segments = useSegments();
  const router = useRouter();
  const notificationListener = useRef<Notifications.Subscription>();
  const responseListener = useRef<Notifications.Subscription>();

  // This useEffect handles redirection and hiding the splash screen
  useEffect(() => {
    if (!isLoading && loaded) { // Only run once authentication state is determined AND fonts are loaded
      const inAuthGroup = segments[0] === '(auth)';

      if (session && inAuthGroup) {
        // Redirect to the main app screen if the user is signed in
        // and trying to access an auth screen.
        router.replace('/(tabs)/home');
      } else if (!session && !inAuthGroup) {
        // Redirect to the sign-in screen if the user is not signed in
        // and trying to access a protected screen.
        router.replace('/(auth)/sign-in');
      }
      SplashScreen.hideAsync(); // Hide splash screen once redirection logic is applied
    }
  }, [isLoading, session, segments, router, loaded]);

  // This useEffect handles push notifications
  useEffect(() => {
    if (session?.user?.id) {
      registerForPushNotificationsAsync(session.user.id);
    }

    // This listener is fired whenever a notification is received while the app is foregrounded
    notificationListener.current = Notifications.addNotificationReceivedListener(handleNotification);

    // This listener is fired whenever a user taps on or interacts with a notification (works when app is foregrounded, backgrounded, or killed)
    responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
      handleNotification(response.notification);
    });

    return () => {
      notificationListener.current?.remove();
      responseListener.current?.remove();
    };
  }, [session]); // Only re-run when session changes

  const handleNotification = (notification: Notifications.Notification) => {
    const { data, title, body } = notification.request.content;
    console.log('Handling notification:', { data, title, body });

    switch (data.type) {
      case 'event_reminder':
        console.log('Event Reminder:', data.eventId);
        if (data.eventId) {
          router.push(`/event/${data.eventId}`);
        }
        break;
      case 'schedule_change':
        console.log('Schedule Change:', data.eventId);
        if (data.eventId) {
          router.push(`/event/${data.eventId}`);
        }
        break;
      case 'announcement':
        console.log('Announcement:', title, body);
        break;
      case 'registration_status':
        console.log('Registration Status:', data.status, data.eventName);
        break;
      case 'chat_message':
        console.log('Chat Message:', data.roomId, data.senderName);
        if (data.roomId) {
          router.push(`/chats/${data.roomId}`);
        }
        break;
      default:
        console.log('Unknown notification type:', data.type);
        break;
    }
  };

  if (isLoading || !loaded) {
    return null; // Still return null while authentication state is being determined or fonts are not loaded
  }

  return (
    <Stack>
      <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
      <Stack.Screen name="(auth)/sign-in" options={{ title: 'Sign In', headerShown: false }} />
      <Stack.Screen name="(auth)/sign-up" options={{ title: 'Sign Up', headerShown: false }} />
      <Stack.Screen name="+not-found" />
    </Stack>
  );
}

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded, error] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  useEffect(() => {
    if (error) throw error;
  }, [error]);

  if (!loaded) {
    return null;
  };

  return (
    <AuthProvider>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
          <RootLayoutNav loaded={loaded} />
          <StatusBar style="auto" />
        </ThemeProvider>
      </QueryClientProvider>
    </AuthProvider>
  );
}
