import { Stack, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { View, Text, ActivityIndicator, StyleSheet, ScrollView, Linking } from 'react-native';
import { Image } from 'expo-image';
import { supabaseBase } from '../../lib/supabase';

interface Speaker {
  id: string;
  name: string;
  title: string;
  company: string;
  image_url: string;
  linkedin_url: string;
  twitter_url: string;
  bio: string;
}

export default function SpeakerDetailScreen() {
  const { id } = useLocalSearchParams();
  const [speaker, setSpeaker] = useState<Speaker | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSpeaker = async () => {
      try {
        const { data, error } = await supabaseBase
          .from('speakers')
          .select('*')
          .eq('id', id)
          .single();

        if (error) {
          throw error;
        }
        setSpeaker(data as Speaker);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchSpeaker();
    }
  }, [id]);

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#0000ff" />
        <Text>Loading speaker details...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centered}>
        <Text style={styles.errorText}>Error: {error}</Text>
      </View>
    );
  }

  if (!speaker) {
    return (
      <View style={styles.centered}>
        <Text>Speaker not found.</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Stack.Screen options={{ title: speaker.name }} />
      {speaker.image_url && (
        <Image source={{ uri: `${process.env.EXPO_PUBLIC_SUPABASE_URL}/storage/v1/object/public/speaker-images/${speaker.image_url}` }} style={styles.speakerImage} />
      )}
      <Text style={styles.speakerName}>{speaker.name}</Text>
      <Text style={styles.speakerTitle}>{speaker.title} at {speaker.company}</Text>
      {speaker.bio && <Text style={styles.speakerBio}>{speaker.bio}</Text>}
      {speaker.linkedin_url && (
        <Text style={styles.link} onPress={() => Linking.openURL(speaker.linkedin_url)}>
          LinkedIn
        </Text>
      )}
      {speaker.twitter_url && (
        <Text style={styles.link} onPress={() => Linking.openURL(speaker.twitter_url)}>
          Twitter
        </Text>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f0f0f0',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  speakerImage: {
    width: 150,
    height: 150,
    borderRadius: 75,
    alignSelf: 'center',
    marginBottom: 16,
  },
  speakerName: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  speakerTitle: {
    fontSize: 18,
    textAlign: 'center',
    color: '#666',
    marginBottom: 16,
  },
  speakerBio: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  link: {
    color: '#007AFF',
    textAlign: 'center',
    marginTop: 8,
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
});
