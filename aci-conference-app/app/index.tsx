import { useEffect } from 'react';
import { useRouter } from 'expo-router';
import { useAuth } from '@/providers/AuthProvider';
import { View, ActivityIndicator } from 'react-native';

export default function IndexScreen() {
  const router = useRouter();
  const { session, isLoading } = useAuth();

  useEffect(() => {
    if (!isLoading) {
      // Once auth state is determined, redirect appropriately
      if (session) {
        console.log('Index: User authenticated, redirecting to home');
        router.replace('/(tabs)/home');
      } else {
        console.log('Index: User not authenticated, redirecting to sign-in');
        router.replace('/(auth)/sign-in');
      }
    }
  }, [isLoading, session, router]);

  // Show loading indicator while determining auth state
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <ActivityIndicator size="large" />
    </View>
  );
}
