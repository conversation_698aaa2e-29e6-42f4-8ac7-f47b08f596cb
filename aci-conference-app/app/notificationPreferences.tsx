import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Switch, ScrollView, Platform } from 'react-native';
import { AppColors, AppFonts } from '../constants/Colors';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: false,
    shouldSetBadge: false,
  }),
});

async function registerForPushNotificationsAsync() {
  let token;

  if (Platform.OS === 'android') {
    Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  if (Device.isDevice) {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }
    if (finalStatus !== 'granted') {
      alert('Failed to get push token for push notification!');
      return;
    }
    token = (await Notifications.getExpoPushTokenAsync()).data;
    console.log(token);
  } else {
    alert('Must use physical device for Push Notifications');
  }

  return token;
}

export default function NotificationPreferencesScreen() {
  const [globalNotifications, setGlobalNotifications] = useState(true);
  const [eventReminders, setEventReminders] = useState(true);
  const [scheduleChanges, setScheduleChanges] = useState(true);
  const [announcements, setAnnouncements] = useState(true);
  const [registrationUpdates, setRegistrationUpdates] = useState(true);
  const [quietHoursEnabled, setQuietHoursEnabled] = useState(false);
  const [expoPushToken, setExpoPushToken] = useState('');
  const [notification, setNotification] = useState<Notifications.Notification | undefined>(undefined);

  const notificationListener = useRef<Notifications.Subscription>();
  const responseListener = useRef<Notifications.Subscription>();

  useEffect(() => {
    registerForPushNotificationsAsync().then(token => setExpoPushToken(token || ''));

    notificationListener.current = Notifications.addNotificationReceivedListener(notification => {
      setNotification(notification);
    });

    responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
      console.log(response);
    });

    return () => {
      notificationListener.current?.remove();
      responseListener.current?.remove();
    };
  }, []);

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.headerTitle}>Notification Preferences</Text>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Global Settings</Text>
        <View style={styles.settingItem}>
          <Text style={styles.settingText}>Enable All Notifications</Text>
          <Switch
            onValueChange={setGlobalNotifications}
            value={globalNotifications}
            trackColor={{ false: AppColors.textHint, true: AppColors.primaryLight }}
            thumbColor={globalNotifications ? AppColors.primary : AppColors.surface}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Category Settings</Text>
        <View style={styles.settingItem}>
          <Text style={styles.settingText}>Event Reminders</Text>
          <Switch
            onValueChange={setEventReminders}
            value={eventReminders && globalNotifications}
            disabled={!globalNotifications}
            trackColor={{ false: AppColors.textHint, true: AppColors.primaryLight }}
            thumbColor={(eventReminders && globalNotifications) ? AppColors.primary : AppColors.surface}
          />
        </View>
        <View style={styles.settingItem}>
          <Text style={styles.settingText}>Schedule Changes</Text>
          <Switch
            onValueChange={setScheduleChanges}
            value={scheduleChanges && globalNotifications}
            disabled={!globalNotifications}
            trackColor={{ false: AppColors.textHint, true: AppColors.primaryLight }}
            thumbColor={(scheduleChanges && globalNotifications) ? AppColors.primary : AppColors.surface}
          />
        </View>
        <View style={styles.settingItem}>
          <Text style={styles.settingText}>Conference Announcements</Text>
          <Switch
            onValueChange={setAnnouncements}
            value={announcements && globalNotifications}
            disabled={!globalNotifications}
            trackColor={{ false: AppColors.textHint, true: AppColors.primaryLight }}
            thumbColor={(announcements && globalNotifications) ? AppColors.primary : AppColors.surface}
          />
        </View>
        <View style={styles.settingItem}>
          <Text style={styles.settingText}>Registration Updates</Text>
          <Switch
            onValueChange={setRegistrationUpdates}
            value={registrationUpdates && globalNotifications}
            disabled={!globalNotifications}
            trackColor={{ false: AppColors.textHint, true: AppColors.primaryLight }}
            thumbColor={(registrationUpdates && globalNotifications) ? AppColors.primary : AppColors.surface}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Quiet Hours</Text>
        <View style={styles.settingItem}>
          <Text style={styles.settingText}>Enable Quiet Hours</Text>
          <Switch
            onValueChange={setQuietHoursEnabled}
            value={quietHoursEnabled && globalNotifications}
            disabled={!globalNotifications}
            trackColor={{ false: AppColors.textHint, true: AppColors.primaryLight }}
            thumbColor={(quietHoursEnabled && globalNotifications) ? AppColors.primary : AppColors.surface}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Notification Details (for testing)</Text>
        <Text>Your Expo push token: {expoPushToken}</Text>
        <Text>Title: {notification && notification.request.content.title} </Text>
        <Text>Body: {notification && notification.request.content.body}</Text>
        <Text>Data: {notification && JSON.stringify(notification.request.content.data)}</Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: AppColors.background,
  },
  headerTitle: {
    ...AppFonts.h1,
    color: AppColors.textPrimary,
    textAlign: 'center',
    paddingVertical: 20,
    backgroundColor: AppColors.surface,
    borderBottomWidth: 1,
    borderBottomColor: AppColors.surfaceVariant,
    marginBottom: 10,
  },
  section: {
    backgroundColor: AppColors.surface,
    marginHorizontal: 10,
    marginVertical: 5,
    borderRadius: 8,
    padding: 15,
  },
  sectionTitle: {
    ...AppFonts.h3,
    color: AppColors.primaryDark,
    marginBottom: 10,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: AppColors.surfaceVariant,
  },
  settingText: {
    ...AppFonts.bodyLarge,
    color: AppColors.textPrimary,
  },
});