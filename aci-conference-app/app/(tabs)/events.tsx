import React, { useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, ActivityIndicator } from 'react-native';
import { useUniqueEventDatesQuery, useEventsQuery } from '@/hooks/events';
import EventCard from '../../components/EventCard';
import SearchBar from '../../components/SearchBar';
import DateSelector from '../../components/DateSelector';
import { router } from 'expo-router';
import { Event } from '../../types/events';
import { useEventFilterStore } from '../../stores/events';
import { useDesignTokens } from '@/constants/DesignTokens';

export default function EventsScreen() {
  const tokens = useDesignTokens();
  const { filters, setFilters } = useEventFilterStore();
  const { data: uniqueEventDates, isLoading: isLoadingDates, isError: isErrorDates, error: errorDates } = useUniqueEventDatesQuery();
  const { data: events, isLoading, isError, error } = useEventsQuery();

  useEffect(() => {
    // Set initial startDate if uniqueEventDates are available and no startDate is set
    if (uniqueEventDates && uniqueEventDates.length > 0 && !filters.startDate) {
      setFilters({ startDate: uniqueEventDates[0] });
    }
  }, [uniqueEventDates, filters.startDate, setFilters]);

  const handleEventPress = (event: Event) => {
    router.push(`/event/${event.id}`);
  };

  if (isLoadingDates || isLoading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color={tokens.colors.primary.main} />
      </View>
    );
  }

  if (isErrorDates || isError) {
    return (
      <View style={styles.centered}>
        <Text style={styles.errorText}>Error: {(errorDates || error)?.message || 'Failed to load data'}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.headerTitle}>Agenda</Text>
      <SearchBar
        searchText={filters.search}
        onSearchTextChange={(text) => setFilters({ search: text })}
      />
      <DateSelector
        selectedDate={filters.startDate || new Date()}
        onSelectDate={(date) => setFilters({ startDate: date })}
        eventDates={uniqueEventDates || []}
      />
      {(!events || events.length === 0) ? (
        <View style={styles.centered}>
          <Text style={styles.emptyText}>No events found for this date.</Text>
        </View>
      ) : (
        <FlatList
          data={events || []}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <EventCard event={item} onPress={handleEventPress} />
          )}
          contentContainerStyle={styles.listContent}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff', // Will be overridden by tokens
  },
  headerTitle: {
    fontSize: 30, // Will be overridden by tokens
    fontWeight: 'bold', // Will be overridden by tokens
    marginHorizontal: 16,
    marginTop: 20,
    marginBottom: 10,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16, // Will be overridden by tokens
    color: 'red', // Will be overridden by tokens
  },
  emptyText: {
    fontSize: 16, // Will be overridden by tokens
    color: 'gray', // Will be overridden by tokens
  },
  listContent: {
    paddingBottom: 20,
  },
});
