import { StyleSheet, ScrollView, StatusBar, TextInput, TouchableOpacity, Dimensions, useColorScheme } from 'react-native';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';

import { ThemedText as Text } from '@/components/ThemedText';
import { ThemedView as View } from '@/components/ThemedView';
import { useDesignTokens } from '@/constants/DesignTokens';

const mainBanner = require('@/assets/images/main-banner.png');

const { width } = Dimensions.get('window');

const navigationItems = [
  { id: 'agenda', title: 'Agenda', icon: 'calendar-outline' },
  { id: 'attendees', title: 'Attendees', icon: 'people-outline' },
  { id: 'speakers', title: 'Speakers', icon: 'person-outline' },
  { id: 'documents', title: 'Documents', icon: 'document-outline' },
  { id: 'sponsors', title: 'Sponsors', icon: 'cash-outline' },
  { id: 'networking', title: 'Networking', icon: 'time-outline' },
];

export default function HomeScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const tokens = useDesignTokens();
  const styles = createStyles(tokens, isDark);
  
  const handleNavigationPress = (id: string) => {
    // TODO: Implement navigation to specific sections
    console.log('Navigate to:', id);
  };

  return (
    <ScrollView style={styles.container}>
      <StatusBar barStyle="light-content" />
      
      {/* Hero Banner */}
      <View style={styles.heroContainer}>
        <Image
          source={mainBanner}
          style={styles.heroImage}
          onError={() => console.log('Image failed to load')}
        />
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons name="search" size={20} color={tokens.colors.text.secondary} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search"
            placeholderTextColor={tokens.colors.text.tertiary}
          />
        </View>
      </View>

      {/* Navigation Grid */}
      <View style={styles.navigationGrid}>
        {navigationItems.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={styles.navigationCard}
            onPress={() => handleNavigationPress(item.id)}
          >
            <View style={styles.cardIconContainer}>
              <Ionicons name={item.icon as any} size={32} color={tokens.colors.text.secondary} />
            </View>
            <Text style={styles.cardTitle}>{item.title}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
  );
}

const createStyles = (tokens: any, isDark: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: tokens.colors.background.secondary,
  },
  heroContainer: {
    height: 200,
    overflow: 'hidden',
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  searchContainer: {
    paddingHorizontal: tokens.layout.screenPadding + tokens.spacing.xs,
    paddingVertical: tokens.layout.screenPadding + tokens.spacing.xs,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: tokens.colors.background.tertiary,
    borderRadius: tokens.components.searchBar.borderRadius * 2,
    paddingHorizontal: tokens.layout.screenPadding,
    paddingVertical: tokens.spacing.sm + tokens.spacing.xs,
    borderWidth: 1,
    borderColor: tokens.colors.border.secondary,
  },
  searchIcon: {
    marginRight: tokens.spacing.sm + tokens.spacing.xs,
  },
  searchInput: {
    flex: 1,
    fontSize: tokens.typography.textStyles.body.fontSize,
    color: tokens.colors.text.primary,
    outlineStyle: 'none',
  },
  navigationGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: tokens.layout.screenPadding,
    paddingBottom: tokens.layout.screenPadding + tokens.spacing.xs,
    justifyContent: 'space-between',
  },
  navigationCard: {
    width: (width - (tokens.layout.screenPadding * 2) - tokens.layout.gridGap) / 2,
    backgroundColor: tokens.colors.surface.card,
    borderRadius: tokens.components.card.borderRadius,
    padding: tokens.layout.screenPadding + tokens.spacing.xs,
    marginBottom: tokens.layout.gridGap,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: tokens.colors.border.primary,
    ...tokens.shadows.sm,
  },
  cardIconContainer: {
    marginBottom: tokens.spacing.sm + tokens.spacing.xs,
  },
  cardTitle: {
    fontSize: tokens.typography.textStyles.body.fontSize,
    fontWeight: tokens.typography.fontWeights.semibold.toString(),
    color: tokens.colors.text.primary,
    textAlign: 'center',
  },
});
