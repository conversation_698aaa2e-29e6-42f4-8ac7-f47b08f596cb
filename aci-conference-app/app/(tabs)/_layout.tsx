import { Tabs } from 'expo-router';
import React from 'react';
import { Platform } from 'react-native';

import { HapticTab } from '@/components/HapticTab';
import { HomeIcon, EventsIcon, SpeakersIcon, ChatsIcon, MoreIcon, ProfileIcon } from '@/components/icons';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { useDesignTokens } from '@/constants/DesignTokens';

export default function TabLayout() {
  const tokens = useDesignTokens();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: tokens.colors.primary.main,
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: {
            // Use a transparent background on iOS to show the blur effect
            position: 'absolute',
          },
          default: {},
        }),
      }}>
      <Tabs.Screen
        name="home"
        options={{
          title: 'Home',
          tabBarIcon: ({ color, focused }) => (
            <HomeIcon
              size={tokens.components.tabBar.iconSize}
              color={color}
              fill={focused ? tokens.colors.primary.main : 'none'}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="events"
        options={{
          title: 'Events',
          tabBarIcon: ({ color, focused }) => (
            <EventsIcon
              size={tokens.components.tabBar.iconSize}
              color={color}
              fill={focused ? tokens.colors.primary.main : 'none'}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="speakers"
        options={{
          title: 'Speakers',
          tabBarIcon: ({ color, focused }) => (
            <SpeakersIcon
              size={tokens.components.tabBar.iconSize}
              color={color}
              fill={focused ? tokens.colors.primary.main : 'none'}
            />
          ),
        }}
      />
      
      <Tabs.Screen
        name="chats"
        options={{
          title: 'Chats',
          tabBarIcon: ({ color, focused }) => (
            <ChatsIcon
              size={tokens.components.tabBar.iconSize}
              color={color}
              fill={focused ? tokens.colors.primary.main : 'none'}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color, focused }) => (
            <ProfileIcon
              size={tokens.components.tabBar.iconSize}
              color={color}
              fill={focused ? tokens.colors.primary.main : 'none'}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="more"
        options={{
          title: 'More',
          tabBarIcon: ({ color, focused }) => (
            <MoreIcon
              size={tokens.components.tabBar.iconSize}
              color={color}
              fill={focused ? tokens.colors.primary.main : 'none'}
            />
          ),
        }}
      />
      
    </Tabs>
  );
}
