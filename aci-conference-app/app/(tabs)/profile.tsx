import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, ActivityIndicator, TextInput, Button, Alert } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useAuth } from '@/providers/AuthProvider';
import { getProfile, updateProfile, getOrganizations, getOrganizationTypes } from '@/services/profile/profile-service';
import { UserProfile, Organization, OrganizationType } from '@/types/profile';
import { useDesignTokens } from '@/constants/DesignTokens';
import { Picker } from '@react-native-picker/picker';
import * as ImagePicker from 'expo-image-picker';
import { supabase } from '@/lib/supabase';
import { Image } from 'expo-image';

// Define Zod schema for validation
const profileSchema = z.object({
  full_name: z.string({ required_error: 'This field is required' }).min(1, 'This field is required'),
  designation: z.string({ required_error: 'This field is required' }).min(1, 'This field is required'),
  sector: z.string({ required_error: 'This field is required' }).min(1, 'This field is required'),
  job_title: z.string({ required_error: 'This field is required' }).min(1, 'This field is required'),
  company: z.string({ required_error: 'This field is required' }).min(1, 'This field is required'),
  phone_number: z.string().optional().or(z.literal('')),
  organization_id: z.string().optional().or(z.literal('')),
  conference_expectation: z.string().optional().or(z.literal('')),
  other_info: z.string().optional().or(z.literal('')),
  activation_code: z.string().optional().or(z.literal('')),
  bio: z.string().optional().or(z.literal('')),
  linkedin_url: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  twitter_url: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
});

type ProfileFormData = z.infer<typeof profileSchema>;

export default function ProfileScreen() {
  const { user } = useAuth();
  const tokens = useDesignTokens();
  const styles = createStyles(tokens);

  const [loadingProfile, setLoadingProfile] = useState(true);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [organizationTypes, setOrganizationTypes] = useState<OrganizationType[]>([]);
  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(undefined);

  const { control, handleSubmit, setValue, formState: { errors, isSubmitting } } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
  });

  useEffect(() => {
    const fetchProfileData = async () => {
      if (!user?.id) return;

      try {
        const [profileData, orgsData, orgTypesData] = await Promise.all([
          getProfile(user.id),
          getOrganizations(),
          getOrganizationTypes(),
        ]);

        if (profileData) {
          Object.keys(profileData).forEach(key => {
            // @ts-ignore
            setValue(key, profileData[key]);
          });
          setAvatarUrl(profileData.avatar_url);
        }
        setOrganizations(orgsData);
        setOrganizationTypes(orgTypesData);
      } catch (error) {
        console.error("Error fetching profile data:", error);
        Alert.alert("Error", "Failed to load profile data.");
      } finally {
        setLoadingProfile(false);
      }
    };

    fetchProfileData();
  }, [user?.id, setValue]);

  const onSubmit = async (data: ProfileFormData) => {
    if (!user?.id) return;

    try {
      await updateProfile(user.id, data);
      Alert.alert("Success", "Profile updated successfully!");
    } catch (error) {
      console.error("Error updating profile:", error);
      Alert.alert("Error", "Failed to update profile.");
    }
  };

  const pickImageAndUpload = async () => {
    if (!user?.id) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.7,
      });

      if (!result.canceled && result.assets && result.assets[0].uri) {
        const uri = result.assets[0].uri;
        const fileName = `${user.id}_${Date.now()}.jpg`;

        // Show loading state
        Alert.alert('Uploading', 'Please wait while we upload your image...');

        // Delete old avatar if it exists
        if (avatarUrl) {
          try {
            // Extract filename from URL, handling query parameters
            const urlParts = avatarUrl.split('/');
            const fileNameWithQuery = urlParts[urlParts.length - 1];
            const oldFileName = fileNameWithQuery.split('?')[0]; // Remove query parameters

            if (oldFileName && oldFileName !== 'undefined') {
              console.log('Deleting old avatar:', `${user.id}/${oldFileName}`);
              await supabase.storage.from('avatars').remove([`${user.id}/${oldFileName}`]);
            }
          } catch (deleteError) {
            console.warn('Could not delete old avatar:', deleteError);
          }
        }

        // Upload new image
        const response = await fetch(uri);
        const blob = await response.blob();
        const { error } = await supabase.storage
          .from('avatars')
          .upload(`${user.id}/${fileName}`, blob, {
            upsert: true,
            cacheControl: '0' // Disable caching for immediate updates
          });

        if (error) {
          Alert.alert('Upload failed', error.message);
          return;
        }

        // Get public URL with cache busting
        const timestamp = Date.now();
        const publicUrl = `${supabase.storage.from('avatars').getPublicUrl(`${user.id}/${fileName}`).data.publicUrl}?t=${timestamp}`;

        // Update database first
        await updateProfile(user.id, { avatar_url: publicUrl });

        // Then update local state
        setAvatarUrl(publicUrl);

        // Force refresh profile data to ensure consistency
        try {
          const refreshedProfile = await getProfile(user.id);
          if (refreshedProfile?.avatar_url) {
            setAvatarUrl(refreshedProfile.avatar_url);
          }
        } catch (refreshError) {
          console.warn('Could not refresh profile data:', refreshError);
        }

        Alert.alert('Success', 'Profile image updated!');
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      Alert.alert('Error', 'Failed to upload image. Please try again.');
    }
  };

  if (loadingProfile) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color={tokens.colors.primary.main} />
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={{ alignItems: 'center', marginBottom: 24 }}>
        <Image
          key={avatarUrl} // Force re-render when URL changes
          source={avatarUrl || 'https://ui-avatars.com/api/?name=User'}
          style={{ width: 96, height: 96, borderRadius: 48, marginBottom: 8 }}
          cachePolicy="none" // Disable caching to ensure fresh images
          contentFit="cover"
        />
        <Button title="Change Profile Image" onPress={pickImageAndUpload} />
      </View>

      <Text style={styles.headerTitle}>Edit Profile</Text>

      <Text style={styles.label}>Full Name</Text>
      <Controller
        control={control}
        name="full_name"
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            style={styles.input}
            onBlur={onBlur}
            onChangeText={onChange}
            value={value || ''}
            placeholder="Your full name"
          />
        )}
      />
      {errors.full_name && <Text style={styles.errorText}>{errors.full_name.message}</Text>}

      <Text style={styles.label}>Phone Number</Text>
      <Controller
        control={control}
        name="phone_number"
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            style={styles.input}
            onBlur={onBlur}
            onChangeText={onChange}
            value={value || ''}
            placeholder="e.g., +1234567890"
            keyboardType="phone-pad"
          />
        )}
      />
      {errors.phone_number && <Text style={styles.errorText}>{errors.phone_number.message}</Text>}

      <Text style={styles.label}>Designation</Text>
      <Controller
        control={control}
        name="designation"
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            style={styles.input}
            onBlur={onBlur}
            onChangeText={onChange}
            value={value || ''}
            placeholder="e.g., Senior Analyst"
          />
        )}
      />
      {errors.designation && <Text style={styles.errorText}>{errors.designation.message}</Text>}

      <Text style={styles.label}>Sector</Text>
      <Controller
        control={control}
        name="sector"
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            style={styles.input}
            onBlur={onBlur}
            onChangeText={onChange}
            value={value || ''}
            placeholder="e.g., Financial Services"
          />
        )}
      />
      {errors.sector && <Text style={styles.errorText}>{errors.sector.message}</Text>}

      <Text style={styles.label}>Organization</Text>
      <Controller
        control={control}
        name="organization_id"
        render={({ field: { onChange, value } }) => (
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={value}
              onValueChange={onChange}
              style={styles.picker}
            >
              <Picker.Item label="Select Organization" value="" />
              {organizations.map((org) => (
                <Picker.Item key={org.id} label={org.name} value={org.id} />
              ))}
            </Picker>
          </View>
        )}
      />
      {errors.organization_id && <Text style={styles.errorText}>{errors.organization_id.message}</Text>}

      <Text style={styles.label}>Conference Expectation</Text>
      <Controller
        control={control}
        name="conference_expectation"
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            style={styles.input}
            onBlur={onBlur}
            onChangeText={onChange}
            value={value || ''}
            placeholder="What do you hope to gain?"
            multiline
          />
        )}
      />
      {errors.conference_expectation && <Text style={styles.errorText}>{errors.conference_expectation.message}</Text>}

      <Text style={styles.label}>Other Info</Text>
      <Controller
        control={control}
        name="other_info"
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            style={styles.input}
            onBlur={onBlur}
            onChangeText={onChange}
            value={value || ''}
            placeholder="Any other relevant information"
            multiline
          />
        )}
      />
      {errors.other_info && <Text style={styles.errorText}>{errors.other_info.message}</Text>}

      <Text style={styles.label}>Activation Code</Text>
      <Controller
        control={control}
        name="activation_code"
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            style={styles.input}
            onBlur={onBlur}
            onChangeText={onChange}
            value={value || ''}
            placeholder="Enter activation code"
          />
        )}
      />
      {errors.activation_code && <Text style={styles.errorText}>{errors.activation_code.message}</Text>}

      <Text style={styles.label}>Bio</Text>
      <Controller
        control={control}
        name="bio"
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            style={styles.input}
            onBlur={onBlur}
            onChangeText={onChange}
            value={value || ''}
            placeholder="Tell us about yourself"
            multiline
          />
        )}
      />
      {errors.bio && <Text style={styles.errorText}>{errors.bio.message}</Text>}

      <Text style={styles.label}>Job Title</Text>
      <Controller
        control={control}
        name="job_title"
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            style={styles.input}
            onBlur={onBlur}
            onChangeText={onChange}
            value={value || ''}
            placeholder="Your job title"
          />
        )}
      />
      {errors.job_title && <Text style={styles.errorText}>{errors.job_title.message}</Text>}

      <Text style={styles.label}>Company</Text>
      <Controller
        control={control}
        name="company"
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            style={styles.input}
            onBlur={onBlur}
            onChangeText={onChange}
            value={value || ''}
            placeholder="Your company"
          />
        )}
      />
      {errors.company && <Text style={styles.errorText}>{errors.company.message}</Text>}

      <Text style={styles.label}>LinkedIn URL</Text>
      <Controller
        control={control}
        name="linkedin_url"
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            style={styles.input}
            onBlur={onBlur}
            onChangeText={onChange}
            value={value || ''}
            placeholder="https://linkedin.com/in/yourprofile"
            keyboardType="url"
            autoCapitalize="none"
          />
        )}
      />
      {errors.linkedin_url && <Text style={styles.errorText}>{errors.linkedin_url.message}</Text>}

      <Text style={styles.label}>Twitter URL</Text>
      <Controller
        control={control}
        name="twitter_url"
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            style={styles.input}
            onBlur={onBlur}
            onChangeText={onChange}
            value={value || ''}
            placeholder="https://twitter.com/yourhandle"
            keyboardType="url"
            autoCapitalize="none"
          />
        )}
      />
      {errors.twitter_url && <Text style={styles.errorText}>{errors.twitter_url.message}</Text>}

      <Button title={isSubmitting ? "Saving..." : "Save Profile"} onPress={handleSubmit(onSubmit)} disabled={isSubmitting} />
    </ScrollView>
  );
}

const createStyles = (tokens: any) => StyleSheet.create({
  container: {
    flex: 1,
    padding: tokens.layout.screenPadding,
    backgroundColor: tokens.colors.background.primary,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: tokens.colors.background.primary,
  },
  headerTitle: {
    fontSize: tokens.typography.fontSizes['2xl'],
    fontWeight: tokens.typography.fontWeights.bold.toString(),
    color: tokens.colors.text.primary,
    marginBottom: tokens.spacing.lg,
    textAlign: 'center',
  },
  label: {
    fontSize: tokens.typography.fontSizes.base,
    fontWeight: tokens.typography.fontWeights.medium.toString(),
    color: tokens.colors.text.secondary,
    marginBottom: tokens.spacing.xs,
    marginTop: tokens.spacing.md,
  },
  input: {
    borderWidth: 1,
    borderColor: tokens.colors.border.primary,
    borderRadius: tokens.components.input.borderRadius,
    paddingVertical: tokens.components.input.paddingVertical,
    paddingHorizontal: tokens.components.input.paddingHorizontal,
    fontSize: tokens.components.input.fontSize,
    color: tokens.colors.text.primary,
    backgroundColor: tokens.colors.background.secondary,
  },
  errorText: {
    color: tokens.colors.status.error,
    fontSize: tokens.typography.fontSizes.sm,
    marginTop: tokens.spacing.xs,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: tokens.colors.border.primary,
    borderRadius: tokens.components.input.borderRadius,
    backgroundColor: tokens.colors.background.secondary,
    marginBottom: tokens.spacing.md,
  },
  picker: {
    height: 50,
    width: '100%',
    color: tokens.colors.text.primary,
  },
});
