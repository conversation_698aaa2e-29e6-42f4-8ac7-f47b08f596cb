import React from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, Linking } from 'react-native';
import { useAuth } from '@/providers/AuthProvider';
import { Ionicons } from '@expo/vector-icons';
import { AppColors, AppFonts } from '../../constants/Colors';
import { router } from 'expo-router';

interface MenuItem {
  title: string;
  route?: string;
  url?: string;
  isExternal: boolean;
}

export default function MoreScreen() {
  const { signOut } = useAuth();

  const menuItems: MenuItem[] = [
    {
      title: "Edit Profile",
      route: "/profile",
      isExternal: false,
    },
    {
      title: "Sponsors and Exhibitors",
      route: "/sponsors-exhibitors",
      isExternal: false,
    },
    {
      title: "Attendees",
      route: "/attendees",
      isExternal: false
    },
    {
      title: "Notification Preferences",
      route: "/notificationPreferences",
      isExternal: false,
    },
    {
      title: "Privacy Policy",
      url: "https://example.com/privacy-policy",
      isExternal: true,
    },
    {
      title: "Acceptable Use Policy",
      url: "https://example.com/acceptable-use-policy",
      isExternal: true,
    },
    {
      title: "ACI Ghana Website",
      url: "https://aci-ghana.com/",
      isExternal: true,
    },
    {
      title: "About this app",
      route: "/about-app",
      isExternal: false
    },
    {
      title: "Logout",
      isExternal: false,
      route: "logout",
    },
  ];

  const onItemTap = (item: MenuItem) => {
    if (item.isExternal) {
      if (item.url) {
        Linking.openURL(item.url);
      }
    } else if (item.route === "logout") {
      signOut();
    } else if (item.route) {
      router.push(item.route);
    }
  };

  const renderItem = ({ item }: { item: MenuItem }) => (
    <TouchableOpacity style={styles.listItem} onPress={() => onItemTap(item)}>
      <Text style={styles.listItemTitle}>{item.title}</Text>
      <Ionicons
        name={item.isExternal ? "open-outline" : "chevron-forward-outline"}
        size={24}
        color={AppColors.textSecondary}
      />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.headerTitle}>Helpful Links</Text>
      <FlatList
        data={menuItems}
        renderItem={renderItem}
        keyExtractor={(item) => item.title}
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: AppColors.background,
  },
  headerTitle: {
    ...AppFonts.h1,
    color: AppColors.textPrimary,
    textAlign: 'center',
    paddingVertical: 10,
    backgroundColor: AppColors.surface,
    borderBottomWidth: 1,
    borderBottomColor: AppColors.surfaceVariant,
  },
  listContent: {
    paddingTop: 10,
  },
  listItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: AppColors.surface,
    borderBottomWidth: 1,
    borderBottomColor: AppColors.surfaceVariant,
  },
  listItemTitle: {
    ...AppFonts.bodyLarge,
    color: AppColors.textPrimary,
  },
});