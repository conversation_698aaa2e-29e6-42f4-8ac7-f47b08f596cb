import React, { useEffect, useState } from 'react';
import { FlatList, Text, View, ActivityIndicator, StyleSheet } from 'react-native';
import { Link } from 'expo-router';
import { Image } from 'expo-image';
import { supabaseBase } from '../../lib/supabase';

interface Speaker {
  id: string;
  name: string;
  title: string;
  company: string;
  image_url: string;
  linkedin_url: string;
  twitter_url: string;
  bio: string;
}

export default function SpeakersScreen() {
  const [speakers, setSpeakers] = useState<Speaker[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSpeakers = async () => {
      try {
        const { data, error } = await supabaseBase.from('speakers').select('*');
        if (error) {
          throw error;
        }
        setSpeakers(data as Speaker[]);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchSpeakers();
  }, []);

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#0000ff" />
        <Text>Loading speakers...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centered}>
        <Text style={styles.errorText}>Error: {error}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={speakers}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <Link href={`/speaker/${item.id}`} style={styles.speakerItem}>
            {item.image_url && (
              <Image source={{ uri: `${process.env.EXPO_PUBLIC_SUPABASE_URL}/storage/v1/object/public/speaker-images/${item.image_url}` }} style={styles.speakerImage} />
            )}
            <Text style={styles.speakerName}>{item.name}</Text>
            <Text style={styles.speakerDetails}>{item.title} at {item.company}</Text>
          </Link>
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f0f0f0',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  speakerItem: {
    backgroundColor: '#fff',
    padding: 16,
    marginBottom: 10,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  speakerName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  speakerDetails: {
    fontSize: 14,
    color: '#666',
  },
  speakerImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginRight: 10,
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
});