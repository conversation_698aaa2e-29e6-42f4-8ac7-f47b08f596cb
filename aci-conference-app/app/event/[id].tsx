import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, ActivityIndicator, TouchableOpacity } from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import { AppColors, AppFonts } from '../../constants/Colors';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { Speaker } from '../../types/events';
import { useEventByIdQuery } from '@/hooks/events';

export default function EventDetailScreen() {
  const { id } = useLocalSearchParams();
  const [activeSpeakerCategory, setActiveSpeakerCategory] = useState<string>('Keynote Speakers');
  const { data: event, isLoading, error } = useEventByIdQuery(id as string);

  if (isLoading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color={AppColors.primary} />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centered}>
        <Text style={styles.errorText}>Error: {error instanceof Error ? error.message : 'Failed to load event details'}</Text>
      </View>
    );
  }

  if (!event) {
    return (
      <View style={styles.centered}>
        <Text style={styles.emptyText}>Event not found.</Text>
      </View>
    );
  }

  const speakerCategories = ['Keynote Speakers', 'Panellists', 'Moderators'];

  const getFilteredSpeakers = (speakers: { speaker: Speaker }[], category: string) => {
    return speakers.filter(s => {
      if (category === 'Keynote Speakers') {
        return s.speaker.title?.includes('Keynote');
      } else if (category === 'Panellists') {
        return s.speaker.title?.includes('Panellist');
      } else if (category === 'Moderators') {
        return s.speaker.title?.includes('Moderator');
      }
      return true;
    });
  };

  const filteredSpeakers = getFilteredSpeakers(event.speakers, activeSpeakerCategory);

  return (
    <ScrollView style={styles.container}>
      {event.image_url && (
        <Image
          source={{ uri: event.image_url }}
          style={styles.heroImage}
          contentFit="cover"
        />
      )}
      <View style={styles.content}>
        <Text style={styles.title}>{event.title}</Text>
        <View style={styles.infoRow}>
          <Ionicons name="calendar-outline" size={18} color={AppColors.textSecondary} />
          <Text style={styles.infoText}>
            {format(new Date(event.start_time), 'dd MMM yyyy')} - {format(new Date(event.end_time), 'dd MMM yyyy')}
          </Text>
        </View>
        <View style={styles.infoRow}>
          <Ionicons name="time-outline" size={18} color={AppColors.textSecondary} />
          <Text style={styles.infoText}>
            {format(new Date(event.start_time), 'hh:mm a')} - {format(new Date(event.end_time), 'hh:mm a')}
          </Text>
        </View>
        <View style={styles.infoRow}>
          <Ionicons name="location-outline" size={18} color={AppColors.textSecondary} />
          <Text style={styles.infoText}>{event.location}</Text>
        </View>

        <View style={styles.actionButtonsContainer}>
          <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="people-outline" size={20} color={AppColors.textPrimary} />
            <Text style={styles.actionButtonText}>Attendees</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="document-text-outline" size={20} color={AppColors.textPrimary} />
            <Text style={styles.actionButtonText}>View Documents</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Description</Text>
        <Text style={styles.description}>{event.description}</Text>

        {event.speakers && event.speakers.length > 0 && (
          <>
            <Text style={styles.sectionTitle}>Speakers</Text>
            <View style={styles.speakerCategoryTabs}>
              {speakerCategories.map((category) => (
                <TouchableOpacity
                  key={category}
                  style={[
                    styles.speakerCategoryTab,
                    activeSpeakerCategory === category && styles.activeSpeakerCategoryTab,
                  ]}
                  onPress={() => setActiveSpeakerCategory(category)}
                >
                  <Text
                    style={[
                      styles.speakerCategoryTabText,
                      activeSpeakerCategory === category && styles.activeSpeakerCategoryTabText,
                    ]}
                  >
                    {category}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {filteredSpeakers.length > 0 ? (
              filteredSpeakers.map((s) => (
                <View key={s.speaker.id} style={styles.speakerContainer}>
                  <Image
                    source={{ uri: s.speaker.image_url || 'https://via.placeholder.com/50' }}
                    style={styles.speakerAvatar}
                    contentFit="cover"
                  />
                  <View>
                    <Text style={styles.speakerName}>{s.speaker.name}</Text>
                    <Text style={styles.speakerTitle}>{s.speaker.title}</Text>
                    {s.speaker.company && <Text style={styles.speakerCompany}>{s.speaker.company}</Text>}
                  </View>
                </View>
              ))
            ) : (
              <Text style={styles.emptyText}>No {activeSpeakerCategory.toLowerCase()} found for this event.</Text>
            )}
          </>
        )}

        <TouchableOpacity style={styles.addToCalendarButton}>
          <Ionicons name="calendar-outline" size={20} color={AppColors.textOnPrimary} />
          <Text style={styles.addToCalendarButtonText}>Add to Calendar</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: AppColors.background,
  },
  heroImage: {
    width: '100%',
    height: 200,
  },
  content: {
    padding: 16,
  },
  title: {
    ...AppFonts.h2,
    color: AppColors.textPrimary,
    marginBottom: 10,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  infoText: {
    ...AppFonts.bodyMedium,
    color: AppColors.textSecondary,
    marginLeft: 8,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 15,
    marginBottom: 15,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: AppColors.surfaceVariant,
  },
  actionButtonText: {
    ...AppFonts.bodyMedium,
    color: AppColors.textPrimary,
    marginLeft: 5,
  },
  sectionTitle: {
    ...AppFonts.h3,
    color: AppColors.textPrimary,
    marginTop: 20,
    marginBottom: 10,
  },
  description: {
    ...AppFonts.bodyMedium,
    color: AppColors.textPrimary,
    lineHeight: 22,
  },
  speakerCategoryTabs: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: AppColors.surfaceVariant,
  },
  speakerCategoryTab: {
    paddingVertical: 10,
    paddingHorizontal: 15,
  },
  activeSpeakerCategoryTab: {
    borderBottomWidth: 2,
    borderBottomColor: AppColors.primary,
  },
  speakerCategoryTabText: {
    ...AppFonts.bodyMedium,
    color: AppColors.textSecondary,
  },
  activeSpeakerCategoryTabText: {
    color: AppColors.primary,
    fontWeight: 'bold',
  },
  speakerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    backgroundColor: AppColors.surface,
    padding: 10,
    borderRadius: 8,
  },
  speakerAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 10,
  },
  speakerName: {
    ...AppFonts.bodyLarge,
    color: AppColors.textPrimary,
  },
  speakerTitle: {
    ...AppFonts.bodyMedium,
    color: AppColors.textSecondary,
  },
  speakerCompany: {
    ...AppFonts.caption,
    color: AppColors.textHint,
  },
  addToCalendarButton: {
    flexDirection: 'row',
    backgroundColor: AppColors.primary,
    padding: 15,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  addToCalendarButtonText: {
    ...AppFonts.bodyLarge,
    color: AppColors.textOnPrimary,
    marginLeft: 10,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    ...AppFonts.bodyMedium,
    color: AppColors.error,
  },
  emptyText: {
    ...AppFonts.bodyMedium,
    color: AppColors.textSecondary,
  },
});