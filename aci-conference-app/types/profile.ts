export interface UserProfile {
  id: string;
  full_name?: string;
  job_title?: string;
  company?: string;
  bio?: string;
  avatar_url?: string;
  linkedin_url?: string;
  twitter_url?: string;
  fcm_token?: string;
  role?: 'attendee' | 'speaker' | 'organizer' | 'admin';
  notification_settings?: object;
  privacy_settings?: object;
  created_at?: string;
  updated_at?: string;
  phone_number?: string;
  designation?: string;
  organization_id?: string;
  sector?: string;
  is_speaker?: boolean;
  conference_expectation?: string;
  other_info?: string;
  activation_code?: string;
}

export interface Organization {
  id: string;
  name: string;
  organization_type_id?: string;
}

export interface OrganizationType {
  id: string;
  name: string;
}
