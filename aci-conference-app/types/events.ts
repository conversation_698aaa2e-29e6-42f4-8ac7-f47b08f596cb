export interface Speaker {
  id: string;
  name: string;
  title: string;
  bio: string;
  image_url?: string;
  linkedin_url?: string;
  twitter_url?: string;
  company?: string;
}

export interface Event {
  id: string;
  title: string;
  description: string;
  start_time: string; // ISO string
  end_time: string; // ISO string
  location: string;
  speakers: { speaker: Speaker }[]; // Supabase join returns an array of objects with a 'speaker' key
  category: string;
  image_url?: string;
  is_favorite?: boolean;
  is_virtual?: boolean;
  meeting_url?: string;
  tags: string[];
  created_at: string;
  updated_at: string;
}