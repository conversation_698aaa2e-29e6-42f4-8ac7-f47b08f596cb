import { useQuery } from '@tanstack/react-query';
import { getUniqueEventDates, getEventById, getEvents } from '@/services/events/events-service';
import { useEventFilterStore } from '@/stores/events';

export const useUniqueEventDatesQuery = () => {
  return useQuery({
    queryKey: ['uniqueEventDates'],
    queryFn: getUniqueEventDates,
    staleTime: Infinity, // Dates don't change often
  });
};

export const useEventByIdQuery = (id: string | undefined) => {
  return useQuery({
    queryKey: ['event', id],
    queryFn: () => (id ? getEventById(id) : Promise.resolve(null)),
    enabled: !!id,
  });
};

export const useEventsQuery = () => {
  const { filters } = useEventFilterStore();
  return useQuery({
    queryKey: ['events', filters],
    queryFn: () => getEvents(filters),
    keepPreviousData: true,
  });
};
