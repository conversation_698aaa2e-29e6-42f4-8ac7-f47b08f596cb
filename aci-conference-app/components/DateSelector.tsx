import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { useDesignTokens } from '../constants/DesignTokens';
import { format, addDays, isSameDay } from 'date-fns';

interface DateSelectorProps {
  selectedDate: Date;
  onSelectDate: (date: Date) => void;
  eventDates: Date[];
}

const DateSelector: React.FC<DateSelectorProps> = ({ selectedDate, onSelectDate, eventDates }) => {
  const tokens = useDesignTokens();
  const styles = createStyles(tokens);
  const dates = eventDates;

  const renderItem = ({ item }: { item: Date }) => {
    const isSelected = isSameDay(item, selectedDate);
    return (
      <TouchableOpacity
        style={[styles.dateItem, isSelected && styles.selectedDateItem]}
        onPress={() => onSelectDate(item)}
      >
        <Text style={[styles.dateText, isSelected && styles.selectedDateText]}>
          {format(item, 'dd')}
        </Text>
        <Text style={[styles.dayText, isSelected && styles.selectedDayText]}>
          {format(item, 'EEE')}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.monthYear}>{format(selectedDate, 'MMMM yyyy')}</Text>
      <FlatList
        horizontal
        showsHorizontalScrollIndicator={false}
        data={dates}
        renderItem={renderItem}
        keyExtractor={(item) => item.toISOString()}
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
};

const createStyles = (tokens: any) => StyleSheet.create({
  container: {
    paddingVertical: tokens.spacing.sm + tokens.spacing.xs,
    backgroundColor: tokens.colors.surface.primary,
  },
  monthYear: {
    fontSize: tokens.typography.textStyles.body.fontSize,
    fontWeight: tokens.typography.textStyles.body.fontWeight.toString(),
    color: tokens.colors.text.primary,
    textAlign: 'center',
    marginBottom: tokens.spacing.sm + tokens.spacing.xs,
  },
  listContent: {
    paddingHorizontal: tokens.layout.screenPadding,
  },
  dateItem: {
    paddingHorizontal: tokens.components.dateChip.paddingHorizontal,
    paddingVertical: tokens.components.dateChip.paddingVertical,
    borderRadius: tokens.components.dateChip.borderRadius,
    marginHorizontal: tokens.spacing.xs,
    backgroundColor: tokens.colors.background.secondary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedDateItem: {
    backgroundColor: tokens.colors.primary.main,
  },
  dateText: {
    fontSize: tokens.typography.textStyles.h3.fontSize,
    fontWeight: tokens.typography.textStyles.h3.fontWeight.toString(),
    color: tokens.colors.text.primary,
  },
  selectedDateText: {
    color: tokens.colors.primary.contrast,
  },
  dayText: {
    fontSize: tokens.typography.textStyles.caption.fontSize,
    fontWeight: tokens.typography.textStyles.caption.fontWeight.toString(),
    color: tokens.colors.text.secondary,
  },
  selectedDayText: {
    color: tokens.colors.primary.contrast,
  },
});

export default DateSelector;
