import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import { Event } from '../types/events';
import { useDesignTokens } from '../constants/DesignTokens';
import { format } from 'date-fns';

interface EventCardProps {
  event: Event;
  onPress: (event: Event) => void;
}

const EventCard: React.FC<EventCardProps> = ({ event, onPress }) => {
  const tokens = useDesignTokens();
  const styles = createStyles(tokens);
  const displaySpeakers = event.speakers.map(s => s.speaker.name).join(', ');

  return (
    <TouchableOpacity style={styles.card} onPress={() => onPress(event)}>
      <View style={styles.header}>
        <Text style={styles.category}>{event.category}</Text>
        <Ionicons name="chevron-forward" size={20} color={tokens.colors.text.secondary} />
      </View>
      <Text style={styles.title}>{event.title}</Text>
      <View style={styles.infoRow}>
        <Ionicons name="calendar-outline" size={16} color={tokens.colors.secondary.main} />
        <Text style={styles.infoText}>
          {format(new Date(event.start_time), 'dd MMM yyyy')}
        </Text>
        <Ionicons name="time-outline" size={16} color={tokens.colors.secondary.main} style={styles.infoIcon} />
        <Text style={styles.infoText}>
          {format(new Date(event.start_time), 'hh:mm a')} - {format(new Date(event.end_time), 'hh:mm a')}
        </Text>
      </View>
      <View style={styles.infoRow}>
        <Ionicons name="location-outline" size={16} color={tokens.colors.secondary.main} />
        <Text style={styles.infoText}>{event.location}</Text>
      </View>
      <View style={styles.footer}>
        <View style={styles.speakerAvatars}>
          {event.speakers.slice(0, 3).map((s, index) => (
            <Image
              key={s.speaker.id}
              source={{ uri: s.speaker.image_url || 'https://via.placeholder.com/30' }}
              style={[styles.avatar, { marginLeft: index > 0 ? -8 : 0 }]} // Overlap avatars
            />
          ))}
          {event.speakers.length > 3 && (
            <View style={[styles.avatar, { backgroundColor: tokens.colors.primary.light, justifyContent: 'center', alignItems: 'center' }]}>
              <Text style={{ fontSize: tokens.typography.fontSizes.xs, color: tokens.colors.primary.contrast, fontWeight: tokens.typography.fontWeights.medium.toString() }}>+{event.speakers.length - 3}</Text>
            </View>
          )}
        </View>
        <View style={styles.attendees}>
          <Ionicons name="people-outline" size={16} color={tokens.colors.text.secondary} />
          <Text style={styles.attendeesText}>Attendees</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const createStyles = (tokens: any) => StyleSheet.create({
  card: {
    backgroundColor: tokens.colors.surface.card,
    borderRadius: tokens.components.card.borderRadius,
    padding: tokens.components.card.padding,
    marginHorizontal: tokens.layout.screenPadding,
    marginBottom: tokens.spacing.sm + tokens.spacing.xs,
    borderWidth: 1,
    borderColor: tokens.colors.border.primary,
    ...tokens.shadows.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: tokens.spacing.sm,
  },
  category: {
    fontSize: tokens.typography.textStyles.caption.fontSize,
    fontWeight: tokens.typography.textStyles.caption.fontWeight.toString(),
    color: tokens.colors.text.secondary,
  },
  title: {
    fontSize: tokens.typography.textStyles.h3.fontSize,
    fontWeight: tokens.typography.textStyles.h3.fontWeight.toString(),
    color: tokens.colors.text.primary,
    marginBottom: tokens.spacing.sm,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: tokens.spacing.xs,
  },
  infoIcon: {
    marginRight: tokens.spacing.xs,
  },
  infoText: {
    fontSize: tokens.typography.textStyles.bodySmall.fontSize,
    fontWeight: tokens.typography.textStyles.bodySmall.fontWeight.toString(),
    color: tokens.colors.text.secondary,
    marginLeft: tokens.spacing.xs,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: tokens.spacing.sm + tokens.spacing.xs,
  },
  speakerAvatars: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: tokens.components.avatar.sizes.md,
    height: tokens.components.avatar.sizes.md,
    borderRadius: tokens.components.avatar.sizes.md / 2,
    borderWidth: 1,
    borderColor: tokens.colors.surface.primary,
  },
  attendees: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  attendeesText: {
    fontSize: tokens.typography.textStyles.bodySmall.fontSize,
    fontWeight: tokens.typography.textStyles.bodySmall.fontWeight.toString(),
    color: tokens.colors.text.secondary,
    marginLeft: tokens.spacing.xs,
  },
});

export default EventCard;
