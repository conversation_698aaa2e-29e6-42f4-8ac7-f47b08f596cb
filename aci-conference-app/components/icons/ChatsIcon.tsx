import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface ChatsIconProps {
  size?: number;
  color?: string;
  fill?: string;
}

const ChatsIcon: React.FC<ChatsIconProps> = ({ size = 24, color = '#000', fill = 'none' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill={fill}>
    <Path d="M21 11.5C21 16.7467 16.7467 21 11.5 21C10.9893 21 10.4863 20.9637 10 20.8941C7.35402 22.1559 4 21.5 4 21.5C4 21.5 4.5 19.1481 5.51957 16.5C3.00346 14.4237 3 11.5 3 11.5C3 6.25329 7.25329 2 12.5 2C17.7467 2 21 6.25329 21 11.5Z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </Svg>
);

export default ChatsIcon;
