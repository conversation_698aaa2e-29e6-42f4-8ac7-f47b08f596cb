import React from 'react';
import Svg, { Path, Rect } from 'react-native-svg';

interface EventsIconProps {
  size?: number;
  color?: string;
  fill?: string;
}

const EventsIcon: React.FC<EventsIconProps> = ({ size = 24, color = '#000', fill = 'none' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill={fill}>
    <Rect x="4" y="5" width="16" height="16" rx="2" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <Path d="M16 3V7" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <Path d="M8 3V7" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <Path d="M4 11H20" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </Svg>
);

export default EventsIcon;
