import React from 'react';
import Svg, { Path, Circle } from 'react-native-svg';

interface SpeakersIconProps {
  size?: number;
  color?: string;
  fill?: string;
}

const SpeakersIcon: React.FC<SpeakersIconProps> = ({ size = 24, color = '#000', fill = 'none' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill={fill}>
    <Path d="M17 21V19C17 16.7909 15.2091 15 13 15H8C5.79086 15 4 16.7909 4 19V21" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <Circle cx="10.5" cy="8" r="4" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <Path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <Path d="M16 8C16.5304 7.05364 16.8284 6.01113 16.8284 4.93853C16.8284 3.86594 16.5304 2.82343 16 1.87695" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </Svg>
);

export default SpeakersIcon;
