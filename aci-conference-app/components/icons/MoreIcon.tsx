import React from 'react';
import Svg, { Circle } from 'react-native-svg';

interface MoreIconProps {
  size?: number;
  color?: string;
  fill?: string;
}

const MoreIcon: React.FC<MoreIconProps> = ({ size = 24, color = '#000', fill = 'none' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill={fill}>
    <Circle cx="12" cy="5" r="1" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <Circle cx="12" cy="12" r="1" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <Circle cx="12" cy="19" r="1" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </Svg>
);

export default MoreIcon;
