import React, { useState } from 'react';
import { View, StyleSheet, Pressable, ActivityIndicator } from 'react-native';
import { ThemedText as Text } from '@/components/ThemedText';

interface ServiceUnavailableProps {
  error?: string;
  onRetry?: () => Promise<void>;
  lastAttempt?: Date | null;
  isDeviceOnline?: boolean;
}

export default function ServiceUnavailable({ error, onRetry, lastAttempt, isDeviceOnline = true }: ServiceUnavailableProps) {
  const [isRetrying, setIsRetrying] = useState(false);

  const handleRetry = async () => {
    if (!onRetry || isRetrying) return;
    
    setIsRetrying(true);
    try {
      await onRetry();
    } finally {
      setIsRetrying(false);
    }
  };
  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Service Unavailable</Text>
        <Text style={styles.message}>
          {error || (isDeviceOnline 
            ? 'Unable to connect to the authentication service. The service may be temporarily unavailable.'
            : 'No internet connection. Please check your network connection and try again.')}
        </Text>
        {lastAttempt && (
          <Text style={styles.timestamp}>
            Last attempt: {lastAttempt.toLocaleTimeString()}
          </Text>
        )}
        {onRetry && (
          <Pressable 
            style={[styles.retryButton, isRetrying && styles.retryButtonDisabled]} 
            onPress={handleRetry}
            disabled={isRetrying}
          >
            {isRetrying ? (
              <ActivityIndicator size="small" color="#007AFF" />
            ) : (
              <Text style={styles.retryButtonText}>
                {isDeviceOnline ? 'Retry Connection' : 'Check Connection'}
              </Text>
            )}
          </Pressable>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f4f4f8',
    padding: 20,
  },
  content: {
    alignItems: 'center',
    maxWidth: 300,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  timestamp: {
    fontSize: 12,
    color: '#666',
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderWidth: 1,
    borderColor: '#007AFF',
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
    minHeight: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  retryButtonDisabled: {
    opacity: 0.6,
    borderColor: '#ccc',
  },
  retryButtonText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
    textAlign: 'center',
  },
});