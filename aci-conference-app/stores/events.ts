import { create } from 'zustand';

interface EventFilters {
  search: string;
  category: string | null;
  startDate: Date | null;
  endDate: Date | null;
}

interface EventFilterState {
  filters: EventFilters;
  setFilters: (filters: Partial<EventFilters>) => void;
  reset: () => void;
}

const initialState = {
  filters: {
    search: '',
    category: null,
    startDate: null,
    endDate: null,
  },
};

export const useEventFilterStore = create<EventFilterState>((set) => ({
  ...initialState,
  setFilters: (newFilters: Partial<EventFilters>) => {
    set((state) => ({ filters: { ...state.filters, ...newFilters } }));
  },
  reset: () => set(initialState),
}));
