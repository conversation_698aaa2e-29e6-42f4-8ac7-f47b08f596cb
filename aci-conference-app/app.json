{"expo": {"name": "aci-conference-app", "slug": "aci-conference-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "aciconferenceapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.aci.conference.app", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-notifications", {"icon": "./assets/images/icon.png", "color": "#ffffff", "sounds": [], "mode": "production"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "9c5660d7-4b75-4c92-9c30-1457d0744d76"}}}}