## PRD Compliance TODOs

- [ ] **Adopt `expo-secure-store`:** Replace `AsyncStorage` with `expo-secure-store` for storing sensitive user tokens.
- [x] **Refactor State Management:** Restructure the Zustand stores to follow the feature-based pattern outlined in the PRD, including data, loading, and error states.
- [x] **Implement Repository Pattern:** Created dedicated repository files for event and profile data fetching logic.
- [x] **Implement Attendee Functionality:** Implemented normalized attendee profiles, organizations, and organization types, including frontend integration for profile management.
- [x] **Simplify Network Handling:** Removed custom network handling logic from `AuthProvider` and `supabase.ts`, relying on React Query's built-in error handling and retry mechanisms.


# Speakers Feature Implementation Plan

This file outlines the steps to implement the "Speakers" feature in the aci-conference-app.

## TODO List

- [x] **Database Setup:**
    - [x] Create a `speakers` table in the Supabase database.
    - [x] Define the schema for the `speakers` table based on `olddb.backup`.
    - [x] Create `speaker_events` and `speaker_roles` tables.
- [x] **Image Migration:**
    - [x] Create a "speaker-images" bucket in Supabase Storage.
    - [x] Upload speaker images from `old.storage/profile-pics/` to the "speaker-images" bucket.
    - [x] Update the `image_url` in the `speakers` table to the new Supabase URLs (relative paths).
- [x] **UI Implementation:**
    - [x] Add "Speakers" tab to main app navigation (`aci-conference-app/app/(tabs)/_layout.tsx`).
    - [x] Create a basic `speakers.tsx` file for the Speakers Screen.
    - [x] Create a screen to display a list of all speakers.
    - [x] Create a "Speaker Detail" screen.
    - [x] Implement the UI components for the speaker list and detail screens.
- [x] **Data Fetching:**
    - [x] Implement the logic to fetch speaker data from the Supabase database.
    - [x] Connect the UI components to the data fetching logic.
- [ ] **Testing:**
    - [ ] Test the "Speakers" feature thoroughly.
    - [ ] Ensure that the speaker list and detail screens are working correctly.
    - [ ] Verify that the images are displayed correctly.

## Conference App Notifications Implementation Plan

This section outlines the steps to implement the comprehensive notification system.

### TODO List

- [ ] **Database Schema (3.2):**
    - [x] Create `push_tokens` table.
    - [x] Create `user_event_registrations` table.
    - [x] Create `notification_logs` table.

- [x] **Supabase Edge Functions (3.4):**
    - [x] Implement `send-push-notification` function.
    - [ ] Implement `handle-registration-changes` function (triggered by `user_event_registrations` table changes).
    - [ ] Implement `handle-schedule-changes` function (triggered by `events` table changes).

- [ ] **Frontend (React Native/Expo) - Core Infrastructure (Phase 1):**
    - [x] Set up `expo-notifications`.
    - [x] Implement push token registration (POST /api/push-tokens).
    - [x] Implement push token update (PUT /api/push-tokens/:id).
    - [x] Implement push token removal (DELETE /api/push-tokens/:id).

- [ ] **Frontend (React Native/Expo) - Local Notifications (Phase 2):**
    - [x] Implement event reminder scheduling (15-min, 5-min warnings).
    - [x] Implement custom reminder scheduling.
    - [x] Implement schedule-based reminders (break endings, day start, check-in).
    - [x] Implement notification preferences UI (global, category, quiet hours, DND).
    - [x] Handle notification permissions.

- [ ] **Frontend (React Native/Expo) - Push Notifications (Phase 3):**
    - [x] Implement handling of registration status notifications.
    - [x] Implement handling of schedule change notifications.
    - [x] Implement handling of conference-wide announcements.
    - [x] Implement deep linking from notifications.

- [ ] **Testing (Phase 5 & 8):**
    - [x] Add unit tests for notification scheduling logic.
    - [ ] Add unit tests for push token management.
    - [ ] Add unit tests for Edge Function functionality.
    - [ ] Add integration tests for end-to-end notification flow.
    - [ ] Add integration tests for database trigger testing.
    - [ ] Add integration tests for real-time sync testing.
    - [ ] Perform device testing for iOS and Android notification behavior.
    - [ ] Test background/foreground scenarios.
    - [ ] Test network connectivity edge cases.

## Production Deployment Issues

- [ ] **Authentication & User Management:**
    - [ ] Configure SMTP settings in production Supabase for email confirmations
    - [ ] Add manual user creation capability for testing/admin purposes
    - [ ] Implement user profile completion flow after signup

## Compliance Issues (for later implementation)

- [ ] **Database Schema:**
    - [ ] Clarify if the `speaker_roles` table is still a requirement as it was present in `olddb.backup` but not explicitly in `PRD.md`.
- [ ] **Security:**
    - [ ] Verify and implement Row Level Security (RLS) policies for the `speakers` table as specified in `PRD.md` (e.g., "Speakers are viewable by everyone" and "Only admins can modify speakers").
- [ ] **Performance & UI/UX Enhancements:**
    - [ ] Implement `lodash debounce` for search functionality on the speaker list.
    - [ ] Add `RefreshControl` for pull-to-refresh functionality on the speaker list.
    - [ ] Integrate `React Native Reanimated` for smooth transitions in speaker UI (if applicable).
    - [ ] Implement `React Query` for caching speaker data to enhance performance and offline capabilities.

# Profile Feature Implementation Plan

This file outlines the steps to implement the "Profile" feature in the aci-conference-app.

## TODO List

- [x] **Database Setup:**
    - [x] Create a `profiles` table in the Supabase database.
    - [x] Define the schema for the `profiles` table based on `olddb.backup`.
- [x] **UI Implementation:**
    - [x] Add "Profile" tab to main app navigation (`aci-conference-app/app/(tabs)/_layout.tsx`).
    - [x] Create a basic `profile.tsx` file for the Profile Screen.
    - [x] Implement the UI components for the profile screen.
- [x] **Data Fetching:**
    - [x] Implement the logic to fetch profile data from the Supabase database.
    - [x] Connect the UI components to the data fetching logic.
- [ ] **Profile Editing:**
    - [ ] Implement profile editing functionality.
    - [ ] Add validation for profile fields (e.g., bio, LinkedIn, Twitter).
    - [ ] Implement combobox for organization selection.
    - [ ] Indicate edit mode and saved state in the UI.
- [ ] **Testing:**
    - [ ] Test the "Profile" feature thoroughly.
    - [ ] Ensure that profile fetching and updating works correctly.
    - [ ] Verify that the UI components render correctly in different states (e.g., loading, error, empty).

## Compliance Issues (for later implementation)

- [ ] **Database Schema:**
    - [ ] Clarify if the `profiles` table schema is up-to-date with the latest PRD requirements.
- [ ] **Security:**
    - [ ] Verify and implement Row Level Security (RLS) policies for the `profiles` table as specified in `PRD.md`.
- [ ] **Performance & UI/UX Enhancements:**
    - [ ] Implement `lodash debounce` for search functionality in profile-related dropdowns.
    - [ ] Add `RefreshControl` for pull-to-refresh functionality on the profile screen.
    - [ ] Integrate `React Native Reanimated` for smooth transitions in profile UI (if applicable).
    - [ ] Implement `React Query` for caching profile data to enhance performance and offline capabilities.
