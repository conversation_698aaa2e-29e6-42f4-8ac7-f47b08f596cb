-- Modify the profiles table to align with the new normalized schema

-- Add new columns
ALTER TABLE profiles
ADD COLUMN organization_id UUID REFERENCES organizations(id);

-- Remove old institution-specific columns
ALTER TABLE profiles
DROP COLUMN IF EXISTS banking_institution,
DROP COLUMN IF EXISTS broker_dealer_institution,
DROP COLUMN IF EXISTS regulatory_institution,
DROP COLUMN IF EXISTS educational_institution,
DROP COLUMN IF EXISTS media_house,
DROP COLUMN IF EXISTS sponsor_institution,
DROP COLUMN IF EXISTS exhibitor_institution,
DROP COLUMN IF EXISTS organization,
DROP COLUMN IF EXISTS other_info; -- This was added by the previous migration, now replaced by the new normalized structure.