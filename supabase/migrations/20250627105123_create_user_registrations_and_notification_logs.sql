-- Create the user_event_registrations table
CREATE TABLE IF NOT EXISTS user_event_registrations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  event_id UUID REFERENCES events(id),
  status TEXT, -- 'registered', 'waitlisted', 'cancelled'
  registered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  notification_preferences JSONB -- timing preferences
);

-- Create the notification_logs table
CREATE TABLE IF NOT EXISTS notification_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  notification_type TEXT, -- 'push' or 'local'
  title TEXT,
  body TEXT,
  data JSONB,
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  delivery_status TEXT -- 'sent', 'delivered', 'failed'
);
