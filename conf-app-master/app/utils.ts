import { ATTENDEES_ENDPOINT, SUPABASE_API_KEY } from "../../conf-app/app.config";

export function isEmailValid(email: string): boolean {
  // 1. Convert email into lowercase
  const lowerCaseEmail = email.toLowerCase();

  // 2. Check if it is correctly formed ie. x@y.z
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const isValid = emailRegex.test(lowerCaseEmail);

  // Return a boolean based on result
  return isValid;
}


export async function isEmailOnServer(email: string): Promise<{status: number, message: string}> {
  try {
    const response = await fetch(`${ATTENDEES_ENDPOINT}?email=eq.${email.toLowerCase()}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        apikey: SUPABASE_API_KEY,
      }
    });

    if (!response.ok) {
      return {
        status: 2,
        message: `Error: ${response.statusText}`
      };
    }

    const data = await response.json();

    if (data.length === 0) {
      return {
        status: 0,
        message: "email not found"
      };
    }

    return {
      status: 1,
      message: "email found"
    };
  } catch (error) {
    return {
      status: 2,
      message: `Error: ${error.message}`
    };
  }
}


export async function updateAttendee(email: string, updateInfo: object) {

  const response = await fetch(`${ATTENDEES_ENDPOINT}?email=eq.${email.toLowerCase()}`, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
      apikey: SUPABASE_API_KEY,
      prefer: "return=minimal"
    },
    body: JSON.stringify(updateInfo)
  });

  //handle success case
  if (response.status > 199 && response.status < 205) {
    return;
 }
// handle error
//TODO: appropriately log or notify admin
throw new Error(`Attendee update failed. email: ${email}, updateInfo: ${JSON.stringify(updateInfo)}`);
}


export function setError(error: string, errorDescription: string = ""): { error: string, errorDescription: string } {
  return { error, errorDescription };
}