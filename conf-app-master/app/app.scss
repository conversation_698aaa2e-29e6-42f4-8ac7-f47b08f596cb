@import '@nativescript/theme/core';
@import '@nativescript/theme/default';

// Define color variables
:root {
  --primary-color: #262753;
  --secondary-color: #f6c019;
  --primary-text-color: #ffffff;
  --secondary-text-color: #000000;
  --font-size-small: 14px;
  --font-size-medium: 16px;
  --font-size-large: 20px;
}

// Google material design fonts
.mds {
  font-family: 'Material Symbols Outlined', 'MaterialSymbolsOutlined';
  font-weight: 100;
}

// Apply global styles
body {
  background-color: var(--primary-color);
  color: var(--primary-text-color);
  font-family: 'Arial', sans-serif;
}

.page {
  background-color: var(--primary-color);
  color: var(--primary-text-color);
}
