<template>
  <Page actionBarHidden="true">
    <MDBottomNavigation :selectedIndex="selectedTab">
      <MDTabStrip>
        <MDTabStripItem
          @tap="onTabTap(0)"
          :class="{
            'active-tab-item': selectedTab === 0,
            'inactive-tab-item': selectedTab !== 0,
          }"
        >
          <Image src.decode="font://&#xe88a;" class="mds" />
        </MDTabStripItem>
        <MDTabStripItem
          @tap="onTabTap(1)"
          :class="{
            'active-tab-item': selectedTab === 1,
            'inactive-tab-item': selectedTab !== 1,
          }"
        >
          <Image src.decode="font://&#xe878;" class="mds" />
        </MDTabStripItem>
        <MDTabStripItem
          @tap="onTabTap(2)"
          :class="{
            'active-tab-item': selectedTab === 2,
            'inactive-tab-item': selectedTab !== 2,
          }"
        >
          <Image src.decode="font://&#xe7f4;" class="mds" />
        </MDTabStripItem>
        <MDTabStripItem
          @tap="onTabTap(3)"
          :class="{
            'active-tab-item': selectedTab === 3,
            'inactive-tab-item': selectedTab !== 3,
          }"
        >
          <Image src.decode="font://&#xe0bf;" class="mds" />
        </MDTabStripItem>
        <MDTabStripItem
          @tap="onTabTap(4)"
          :class="{
            'active-tab-item': selectedTab === 4,
            'inactive-tab-item': selectedTab !== 4,
          }"
        >
          <Image src.decode="font://&#xe5d4;" class="mds" />
        </MDTabStripItem>
      </MDTabStrip>

      <!-- Content items -->
      <MDTabContentItem>
        <HomePage
          @navigateToTab="onNavigateToTab"
          @navigateToPage="onNavigateToPage"
        />
      </MDTabContentItem>
      <MDTabContentItem>
        <EventListingPage
          :events="conferenceEvents"
          @itemTap="goToSessionDetailPage"
        />
      </MDTabContentItem>
      <MDTabContentItem>
        <NotificationsPage />
      </MDTabContentItem>
      <MDTabContentItem>
        <ChatsPage />
      </MDTabContentItem>
      <MDTabContentItem> <MoreInfoPage /> </MDTabContentItem>
    </MDBottomNavigation>
  </Page>
</template>

<script lang="ts">
import { SUPABASE_API_KEY, EVENTS_ENDPOINT } from "../app.config";
import Vue from "nativescript-vue";
import HomePage from "./components/HomePage.vue";
import EventListingPage from "./components/EventListingPage.vue";
import NotificationsPage from "./components/NotificationsPage.vue";
import ChatsPage from "./components/ChatsPage.vue";
import MoreInfoPage from "./components/MoreInfoPage.vue";
import EventDetailPage from "./components/EventDetailPage.vue";
import ProfilePage from "./components/ProfilePage.vue";
import { ItemEventData } from "@nativescript/core";

interface ConferenceEvent {
  id: number;
  name: string;
  date: string;
  venue: string;
  start_time: string;
  end_time: string;
  created_at: string;
}

export default Vue.extend({
  components: {
    HomePage,
    EventListingPage,
    NotificationsPage,
    ChatsPage,
    MoreInfoPage,
    ProfilePage,
  },
  data() {
    return {
      selectedTab: 0,
      conferenceEvents: [] as ConferenceEvent[],
    };
  },
  created() {
    this.fetchConferenceEvents();
  },
  methods: {
    onTabTap(index: number): void {
      this.selectedTab = index;
    },
    onNavigateToTab(tabIndex: number): void {
      this.selectedTab = tabIndex;
    },
    onNavigateToPage(page: string): void {
      console.log("onNavigateToPage reached..");
      if (page === "ProfilePage") {
        this.$navigateTo(ProfilePage);
      }
    },

    goToSessionDetailPage(event: ItemEventData): void {
      this.$navigateTo(EventDetailPage);
    },

    async fetchConferenceEvents(): Promise<void> {
      try {
        const response = await fetch(EVENTS_ENDPOINT, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            apikey: SUPABASE_API_KEY,
          },
        });
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = (await response.json()) as ConferenceEvent[];
        this.conferenceEvents = data;
      } catch (error) {
        console.error("Error fetching conference events:", error);
        // Handle the error appropriately
      }
    },
  },
});
</script>

<style scoped lang="scss">
.inactive-tab-item {
  background-color: #262753;
  color: white;
}

.active-tab-item {
  background-color: #f6c019;
  color: black;
}

.mds {
  width: 24;
  height: 24;
}

.action-bar-title {
  font-size: 20;
  font-weight: bold;
  color: white;
}

.list-item {
  margin: 5;
  padding: 5;
}

.chevron-right {
  font-size: 24;
  color: gray;
}
</style>
