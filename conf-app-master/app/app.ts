import Vue from 'nativescript-vue';
import { ApplicationSettings } from '@nativescript/core';
import BottomNavigation from '@nativescript-community/ui-material-bottom-navigation/vue';
import EmailCheckPage from './components/EmailCheckPage.vue';
import VerificationPage from './components/VerificationPage.vue';
import SetupPage from './components/SetupPage.vue';
import LoginPage from './components/LoginPage.vue';
import App from './App.vue';
import { APP_ID_PREFIX } from '/app.config';
import { SetupStatus, SetupStatusOrEmpty } from '../types/Attendee';

Vue.use(BottomNavigation);

declare let __DEV__: boolean;

// Prints Vue logs when --env.production is *NOT* set while building
Vue.config.silent = !__DEV__;

// Map of status to components
const statusComponentMap = {
    [SetupStatus.REGISTERED]: VerificationPage,
    [SetupStatus.VERIFIED]: SetupPage,
    [SetupStatus.SETUP]: App,
    [SetupStatus.LOGGED_IN]: App,
    "": EmailCheckPage
};

//check the conference app status 
const statusKey = `${APP_ID_PREFIX}SETUP_STATUS`;
const statusKeyValue: SetupStatusOrEmpty = ApplicationSettings.getString(statusKey, "") as SetupStatusOrEmpty;

// Function to get the appropriate component
function getStartComponent(status: SetupStatusOrEmpty) {
    switch (status) {
        case SetupStatus.REGISTERED:
            console.log("User is registered but not verified");
            return statusComponentMap[SetupStatus.REGISTERED];
        case SetupStatus.VERIFIED:
            console.log("User is verified but hasn't completed setup");
            return statusComponentMap[SetupStatus.VERIFIED];
        case SetupStatus.SETUP:
          console.log("User has completed setup")
          return statusComponentMap[SetupStatus.SETUP];
        case SetupStatus.LOGGED_IN:
            console.log("User is logged in");
            return statusComponentMap[SetupStatus.LOGGED_IN];
        case "":
            console.log("No status found, user is new or status was cleared");
            return statusComponentMap[""];
        default:
            console.log("Unknown status:", status);
            return EmailCheckPage; // Default to EmailCheckPage in case of unknown status
    }
}

const StartComponent = getStartComponent(statusKeyValue);

new Vue({
    render: h => h('frame', [h(StartComponent)])
}).$start();