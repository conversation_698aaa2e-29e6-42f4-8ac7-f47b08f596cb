<template>
  <Page actionBarHidden="true">
    <StackLayout>
      <StackLayout height="100">
        <Image
          src="~/assets/conference-banner.png"
          stretch="aspectFit"
          height="100"
        />
      </StackLayout>
      <StackLayout class="page-content">
        <Label text="Please login to your account" class="paragraph" />

        <StackLayout class="input-container">
          <TextField
            v-model="email"
            hint="Email"
            keyboardType="email"
            class="input"
          />
        </StackLayout>

        <GridLayout rows="auto" columns="*, auto" class="input-container">
          <TextField
            v-model="password"
            :secure="!showPassword"
            hint="Password"
            class="input"
            col="0"
          />
          <Button
            :text="showPassword ? 'Hide' : 'Show'"
            @tap="togglePasswordVisibility"
            class="show-hide-button"
            col="1"
          />
        </GridLayout>

        <Button text="Login" @tap="login" class="button" />

        <Label
          text="Forgot Password?"
          class="forgot-password"
          @tap="forgotPassword"
        />
      </StackLayout>
    </StackLayout>
  </Page>
</template>

<script lang="ts">
import Vue from "nativescript-vue";
import { login } from "../auth";
import { ApplicationSettings } from "@nativescript/core";
import App from "../App.vue";

export default Vue.extend({
  data() {
    return {
      email: "",
      password: "",
      showPassword: false,
    };
  },
  methods: {
    async login() {
      try {
        const response = await login(this.email, this.password);
        console.log("Login successful", response);

        // Set the is_logged_in flag
        ApplicationSettings.setBoolean("is_logged_in", true);

        // Redirect to App.vue
        this.$navigateTo(App, { clearHistory: true });
      } catch (error) {
        if (error instanceof Error) {
          alert(error.message);
        } else {
          console.error("Unexpected error", error);
        }
      }
    },
    togglePasswordVisibility() {
      this.showPassword = !this.showPassword;
    },
    forgotPassword() {
      console.log("Forgot password tapped");
      // TODO: Implement forgot password functionality
    },
  },
});
</script>
<style scoped>
.page-content {
  padding: 10;
}

.m-lr-15 {
  margin-left: 15;
  margin-right: 15;
}
.form-container {
  margin-top: 20;
  margin-bottom: 20;
}

.center {
  text-align: center;
}

.title {
  color: #ffffff;
  font-size: 24;
  text-align: center;
  margin-bottom: 5;
}

.heading1 {
  color: #ffffff;
  font-size: 18;
}

.paragraph {
  color: #ffffff;
  text-align: center;
}

.form-container {
  margin-top: 20;
}

.label {
  color: #ffffff;
  margin-top: 10;
  margin-bottom: 5;
}
.mds {
  width: 24;
  height: 24;
}

.input {
  font-size: 14;
  background-color: white;
  color: #36454f;
  margin-bottom: 10;
  height: 50;
  padding-left: 10;
  padding-right: 10;
  border-radius: 2; /* Rounded corners */
  border-width: 0;
  border-color: #e0e0e0;
}

.button {
  background-color: #f6c019;
  color: black;
  margin-top: 20;
  border-radius: 14;
  height: 50;
}

.error-container {
  margin-top: 10;
}

.error-icon {
  color: #ff3b30;
  font-size: 16;
  margin-right: 5;
}

.error-message {
  color: #ff3b30;
  font-size: 14;
}
</style>
