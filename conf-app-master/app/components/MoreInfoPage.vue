<template>
  <GridLayout rows="auto, *">
    <Label
      text="Helpful Links"
      textAlignment="center"
      class="action-bar-label"
      row="0"
    />
    <ListView for="item in menuItems" @itemTap="onItemTap" row="1">
      <v-template>
        <GridLayout columns="*, auto" rows="auto" class="list-item">
          <Label :text="item.title" class="title" col="0" />
          <Label
            :text="item.isExternal ? 'open_in_new' : 'chevron_right'"
            class="mds"
            col="1"
          />
        </GridLayout>
      </v-template>
    </ListView>
  </GridLayout>
</template>

<script lang="ts">
import Vue from "nativescript-vue";
import { Utils } from "@nativescript/core";

interface MenuItem {
  title: string;
  route?: string;
  url?: string;
  isExternal: boolean;
}

export default Vue.extend({
  data() {
    return {
      menuItems: [
        {
          title: "Sponsors and Exhibitors",
          route: "SponsorsExhibitors",
          isExternal: false,
        },
        
        { title: "Speakers", route: "Speakers", isExternal: false },
        { title: "Attendees", route: "Attendees", isExternal: false },
        {
          title: "Privacy Policy",
          url: "https://example.com/privacy-policy",
          isExternal: true,
        },
        {
          title: "Acceptable Use Policy",
          url: "https://example.com/acceptable-use-policy",
          isExternal: true,
        },
        {
          title: "ACI Ghana Website",
          url: "https://aci-ghana.com/",
          isExternal: true,
        },
        { title: "About this app", route: "AboutSolution", isExternal: false }
      ] as MenuItem[],
    };
  },
  methods: {
    onItemTap(event: { index: number }) {
      const tappedItem = this.menuItems[event.index];
      if (tappedItem.isExternal) {
        this.openExternal(tappedItem.url);
      } else {
        this.navigateTo(tappedItem.route);
      }
    },
    navigateTo(route: string) {
      // Your navigation logic here
      console.log(`Navigating to ${route}`);
      // this.$navigateTo(...);
    },
    openExternal(url: string) {
      if (url) {
        Utils.openUrl(url);
      }
    },
  },
});
</script>
<style scoped>
.action-bar-label {
  background-color: #262753;
  color: white;
  font-size: 20;
  padding: 10;
  margin: 0;
  height: 56;
  vertical-align: center;
}
.list-item {
  padding: 16;
  background-color: white;
  border-bottom-width: 1;
  border-bottom-color: #e0e0e0;
}

.title {
  font-size: 16;
  color: black;
}

.mds {
  font-size: 24;
  color: #888;
  vertical-align: middle;
}
</style>
