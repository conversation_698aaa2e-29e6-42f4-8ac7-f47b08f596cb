<template>
  <Page actionBarHidden="true" backgroundColor="#262753">
    <StackLayout>
      <StackLayout height="100">
        <Image
          src="~/assets/conference-banner.png"
          stretch="aspectFit"
          height="100"
        />
      </StackLayout>

      <StackLayout class="page-content">
        <Label text="Nice!" class="title" />
        <Label :text="registrationMsg" class="paragraph" />
        <Label
          text="Kindly create a password to complete account setup"
          class="paragraph"
        />

        <GridLayout rows="auto, auto" columns="*" class="form-container">
          <ErrorAlert
            class="m-lr-15"
            v-if="error"
            :error="error"
            :description="errorDescription"
            row="0"
          />
          <GridLayout
            rows="auto"
            columns="*, auto"
            row="1"
            class="input-container"
          >
            <TextField
              v-model="password"
              :secure="isPasswordSecure"
              hint="Enter password"
              class="input"
              col="0"
            />
            <Button
              :text="showPassword ? 'Hide' : 'Show'"
              @tap="togglePasswordVisibility"
              class="show-hide-button"
              col="1"
            />
          </GridLayout>
        </GridLayout>

        <Button text="Sign Up" @tap="signUp" class="button" />
      </StackLayout>
    </StackLayout>
  </Page>
</template>

<script lang="ts">
import Vue from "nativescript-vue";
import { alert, ApplicationSettings } from "@nativescript/core";
import { signUp } from "../auth";
import LoginPage from "./LoginPage.vue";
import ErrorAlert from "./ErrorAlert.vue";
import { APP_ID_PREFIX } from "../../app.config";
import { isEmailOnServer, updateAttendee } from "../utils";

export default Vue.extend({
  components: {
    ErrorAlert,
  },
  computed: {
    isPasswordSecure(): boolean {
      return !this.showPassword;
    },
  },
  data() {
    return {
      localEmail: "",
      registrationMsg: "",
      password: "",
      showPassword: false,
      error: "",
      errorDescription: "",
    };
  },
  created() {
    this.loadEmail();
  },
  methods: {
    async loadEmail() {
      this.localEmail = ApplicationSettings.getString("attendee_email", "");

      if (this.localEmail) {
        const response = await isEmailOnServer(this.localEmail);

        if (response.status === 1) {
          this.registrationMsg = `Your email ${this.localEmail} is a valid conference email!`;
        } else if (response.status === 0) {
          this.registrationMsg = `Your email ${this.localEmail} is not found in the conference attendees!`;
          //TODO: shouldnt we redirect and emtpy the attendee_email recod on the device?
        } else {
          this.registrationMsg = response.message;
        }
      } else {
        this.registrationMsg = "Your email is not valid!";
      }
    },

    async signUp() {
      try {
        if (!this.localEmail) {
          this.error = "Email is not available";
          this.errorDescription = "Please try agaiEachn";
          return;
        }

        const response = await signUp(this.localEmail, this.password);
        console.log(response);

        try {
          await updateAttendee(this.email.toLowerCase(), {
            setup_status: "SETUP",
          });
          // The update was successful if we reach this point
          console.log("Attendee updated successfully");

          // Update status on client to VERIFIED
          const statusKey = `${APP_ID_PREFIX}SETUP_STATUS`;
          ApplicationSettings.setString(statusKey, "SETUP");

          this.$navigateTo(LoginPage, { clearHistory: true });
        } catch (error) {
          if (error instanceof Error) {
            console.error("Attendee update failed:", error.message);
            this.setError("Update Failed", error.message);
          } else {
            console.error("An unexpected error occurred:", error);
            this.setError(
              "Unexpected Error",
              "An unknown error occurred while updating attendee information."
            );
          }
        }

        alert({
          title: "Great!",
          message: "Kindly check your email to confirm account setup",
          okButtonText: "Login",
        }).then(() => {
          this.$navigateTo(LoginPage, { clearHistory: true });
        });
      } catch (error) {
        if (error instanceof Error) {
          console.log(error);
          this.error = "Sign Up Failed";
          this.errorDescription = error.message;
        } else {
          console.error("Unexpected error", error);
          this.error = "Unexpected Error";
          this.errorDescription = "Please try again later";
        }
      }
    },
    togglePasswordVisibility() {
      this.showPassword = !this.showPassword;
    },
  },
});
</script>

<style scoped>
.page-content {
  padding: 10;
}

.m-lr-15 {
  margin-left: 15;
  margin-right: 15;
}

.form-container {
  margin-top: 20;
  margin-bottom: 20;
}

.title {
  color: #ffffff;
  font-size: 24;
  text-align: center;
  margin-bottom: 5;
}

.paragraph {
  color: #ffffff;
  text-align: center;
}

.input-container {
  margin-bottom: 10;
}

.input {
  font-size: 14;
  background-color: white;
  color: #36454f;
  margin-bottom: 10;
  height: 50;
  padding-left: 10;
  padding-right: 10;
  border-radius: 2 0 0 2;
  border-width: 0;
  border-color: #e0e0e0;
}

.show-hide-button {
  height: 50;
  width: 60;
  font-size: 14;
  background-color: #f0f0f0;
  color: #36454f;
  border-width: 0;
  border-radius: 0 2 2 0;
  padding: 0;
  margin: 0;
}

.button {
  background-color: #f6c019;
  color: black;
  margin-top: 20;
  border-radius: 14;
  height: 50;
}
</style>
