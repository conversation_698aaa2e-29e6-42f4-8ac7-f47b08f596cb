<template>
  <ScrollView>
    <StackLayout>
      <StackLayout backgroundColor="#262753">
        <Image src="~/assets/conference-banner.png" stretch="aspectFit" height="100" />
      </StackLayout>
      
      <GridLayout
        columns="*, *"
        rows="auto, auto, auto, auto"
        horizontalAlignment="center"
        width="95%"
        class="tiles-container"
      >
        <Label :text="'Hi ' + fullname+'!'" class="title" row="0" col="0" colSpan="2"/>

        <StackLayout
          @tap="goToEventListingPage"
          class="tile"
          row="1"
          col="0"
        >
          <Label text.decode="&#xe878;" class="mds" />
          <Label text="Event Agenda" textWrap="true" />
        </StackLayout>

        <StackLayout
          @tap="goToNotificationsPage"
          class="tile"
          row="1"
          col="1"
        >
          <Label text.decode="&#xe7f4;" class="mds" />
          <Label text="Notifications" textWrap="true" />
        </StackLayout>

        <StackLayout
          @tap="goToNetworkingPage"
          class="tile"
          row="2"
          col="0"
        >
          <Label text.decode="&#xe8d3;" class="mds" />
          <Label text="Networking" textWrap="true" />
        </StackLayout>

        <StackLayout
          @tap="goToInfoPage"
          class="tile"
          row="2"
          col="1"
        >
          <Label text.decode="&#xe88e;" class="mds" />
          <Label text="Information" textWrap="true" />
        </StackLayout>

        <StackLayout
          @tap="goToProfilepage"
          class="tile"
          row="3"
          col="0"
        >
          <Label text.decode="&#xe7fd;" class="mds" />
          <Label text="My Profile" textWrap="true" />
        </StackLayout>
      </GridLayout>
    </StackLayout>
  </ScrollView>
</template>

<script lang="ts">
import { StackLayout, ApplicationSettings } from "@nativescript/core";

export default {
  data() {
    return {
      fullname: ''
    };
  },

  created() {
    this.loadFullName();
  },

  methods: {
    loadFullName() {
      // Read the fullname from Application.Settings
      this.fullname = ApplicationSettings.getString("attendee_name", "");
    },

    goToEventListingPage() {
      this.$emit('navigateToTab', 1)
    },

    goToNotificationsPage() {
      this.$emit('navigateToTab', 2)
    },

    goToNetworkingPage() {
      this.$emit('navigateToTab', 3)
    },

    goToInfoPage() {
      this.$emit('navigateToTab', 4)
    },

    goToProfilepage() {
      this.$emit('navigateToPage', 'ProfilePage')
    },
  },
};
</script>

<style scoped>
.mds {
  font-family: "Material Symbols Outlined", "MaterialSymbolsOutlined";
  font-weight: normal;
  font-size: 40;
  color: #262753;
}
.title {
  color: #262753;
  font-size: 24;
  margin-bottom: 5;
  margin-left: 5;
}

.tiles-container {
  margin-top: 20;

}

.tile {
  background-color: #f6c019;
  color: #262753;
  border-radius: 1;
  margin: 5;
  padding: 20;
  text-align: center;
}

.tile Label {
  font-size: 18;
}
</style>
