<template>
  <Page actionBarHidden="true" backgroundColor="#262753">
    <StackLayout>
      <StackLayout  height="100">
        <Image src="~/assets/conference-banner.png" stretch="aspectFit" height="100" />
      </StackLayout>
      <StackLayout class="page-content">
        <Label text="Hi!" class="title"/>
        <!-- <Label text="Let's get you setup on the official app for the" class="paragraph"/> -->
        <Label text="Let's get you setup on the Conference App" class="heading1 center"/>
        
        <GridLayout rows="auto, auto, auto" columns="*, *" class="form-container">
          <GridLayout v-if="error" rows="auto" columns="auto, *" row="0" colSpan="2" class="error-container">
            <Image src.decode="font://&#xe000;" class="mds" col="0" />
            <Label :text="error" class="error-message" col="1" />
          </GridLayout>
          <TextField v-model="email" class="input" row="1" col="0" colSpan="2" hint="Enter your email"/>
          <TextField v-model="activationCode" class="input" row="2" col="0" colSpan="2" hint="Enter registration code"/>
        </GridLayout>

        <Button text="Verify Registration" @tap="verifyUser" class="button"/>
      </StackLayout>
    </StackLayout>
  </Page>
</template>

<script lang="ts">
import { ApplicationSettings, StackLayout, TextField } from "@nativescript/core";
import { validateRegistration } from "../auth";


import SetupPage from "./SetupPage.vue";
import { Attendee } from "types/Attendee";

export default {
  data() {
    return {
      email: "",
      activationCode: "",
      error: "",
    };
  },
  methods: {
    async verifyUser(): Promise<void> {
    try {
      const response = await validateRegistration(
        this.email,
        this.activationCode
      );
      if(response.length < 1){
        console.log(response)
        throw new Error("Invalid Registration details");
      }
      console.log(response);
      let attendee: Attendee = response[0]
      
      // Set the is_verified flag as well as attendee details
      //update verification status on server
      
      ApplicationSettings.setBoolean("is_verified", true);
      ApplicationSettings.setString("attendee_email", attendee.email)
      ApplicationSettings.setString("attendee_name", attendee.fullname)
      
      this.goToSetupPage();
    } catch (error) {
      if (error instanceof Error) {
        this.error = error.message;
      } else {
        console.error("Unexpected error", error);
      }
    }
  },
    goToSetupPage(): void {
      const email = this.email;
      this.$navigateTo(SetupPage, {
        props: {
          email,
        },
      });
    },
  },
};
</script>

<style scoped>
.page-content {
    padding: 10;
}

.center {
  text-align: center;
}

.title {
    color: #ffffff;
    font-size: 24;
    text-align: center;
    margin-bottom: 5;
}

.heading1 {
  color: #ffffff;
  font-size: 18;
}

.paragraph {
  color: #ffffff;
  text-align: center;
}

.form-container {
  margin-top: 20;
}

.label {
    color: #ffffff;
    margin-top: 10;
    margin-bottom: 5;
}
.mds {
  width: 24;
  height: 24;
}

.input {
    font-size: 14;
    background-color: white;
    color: #36454F;
    margin-bottom: 10;
    height: 50;
    padding-left: 10;
    padding-right: 10;
    border-radius: 2; /* Rounded corners */
    border-width: 0;
    border-color: #e0e0e0; 
}

.button {
    background-color: #f6c019;
    color: black;
    margin-top: 20;
    border-radius: 14;
    height: 50;
}

.error-container {
  margin-top: 10;
}

.error-icon {
  color: #ff3b30;
  font-size: 16;
  margin-right: 5;
}

.error-message {
  color: #ff3b30;
  font-size: 14;
}
</style>
