<template>
  <Page actionBarHidden="true" backgroundColor="#262753">
    <StackLayout>
      <StackLayout height="100">
        <Image
          src="~/assets/conference-banner.png"
          stretch="aspectFit"
          height="100"
        />
      </StackLayout>

      <StackLayout class="page-content">
        <Label text="Welcome!" class="title" />

        <GridLayout rows="auto, auto" columns="*" class="form-container">
          <ErrorAlert
            class="m-lr-15"
            v-if="error"
            :error="error"
            :description="errorDescription"
            row="0"
          />
          <TextField
            v-model="email"
            class="input"
            row="1"
            hint="Enter your email"
          />
        </GridLayout>

        <Button text="Verify Email" @tap="verifyEmail" class="button" />
      </StackLayout>
    </StackLayout>
  </Page>
</template>

<script lang="ts">
import { ApplicationSettings } from "@nativescript/core";
import { getAttendeeStatus } from "../auth";
import { isEmailValid, updateAttendee, setError } from "../utils";
import ErrorAlert from "./ErrorAlert.vue";
import SetupPage from "./SetupPage.vue";
import { Attendee } from "types/Attendee";
import { APP_ID_PREFIX } from "../../app.config";

export default {
  components: {
    ErrorAlert,
  },
  data() {
    return {
      email: "",
      error: "",
      errorDescription: "",
    };
  },
  methods: {
    async verifyEmail(): Promise<void> {
      try {
        if (!isEmailValid(this.email)) {
          ({ error: this.error, errorDescription: this.errorDescription } = setError(
            "Invalid Email",
            "You did not input a valid email address"
          ));
          return;
        }

        const attendeeStatusResponse = await getAttendeeStatus(this.email.toLowerCase());

        if (attendeeStatusResponse.length < 1) {
          ({ error: this.error, errorDescription: this.errorDescription } = setError(
            "Email not registered",
            "Have you registered on the official conference site?"
          ));
          return;
        }

        const attendee: Attendee = attendeeStatusResponse[0];

        try {
          await updateAttendee(this.email.toLowerCase(), { setup_status: "VERIFIED" });
          console.log("Attendee updated successfully");

          const statusKey = `${APP_ID_PREFIX}SETUP_STATUS`;
          ApplicationSettings.setString(statusKey, "VERIFIED");
          ApplicationSettings.setString("attendee_email", this.email.toLowerCase());
          ApplicationSettings.setString("attendee_name", attendee.fullname);

          this.$navigateTo(SetupPage, { clearHistory: true });
        } catch (error) {
          if (error instanceof Error) {
            console.error("Attendee update failed:", error.message);
            ({ error: this.error, errorDescription: this.errorDescription } = setError(
              "Update Failed", 
              error.message
            ));
          } else {
            console.error("An unexpected error occurred:", error);
            ({ error: this.error, errorDescription: this.errorDescription } = setError(
              "Unexpected Error",
              "An unknown error occurred while updating attendee information."
            ));
          }
        }
      } catch (error) {
        if (error instanceof Error) {
          if (error.message === "Network request failed") {
            ({ error: this.error, errorDescription: this.errorDescription } = setError(
              error.message,
              "Kindly check your internet connection"
            ));
          } else {
            ({ error: this.error, errorDescription: this.errorDescription } = setError(
              "Unexpected Error",
              error.message
            ));
          }
        } else {
          console.error("An unidentified error occurred:", error);
          ({ error: this.error, errorDescription: this.errorDescription } = setError(
            "System Error", 
            "Kindly restart app"
          ));
        }
      }
    },
  },
};
</script>

<style scoped>
.page-content {
  padding: 10;
}

.m-lr-15 {
  margin-left: 15;
  margin-right: 15;
}
.form-container {
  margin-top: 20;
  margin-bottom: 20;
}

.center {
  text-align: center;
}

.title {
  color: #ffffff;
  font-size: 24;
  text-align: center;
  margin-bottom: 5;
}

.heading1 {
  color: #ffffff;
  font-size: 18;
}

.paragraph {
  color: #ffffff;
  text-align: center;
}

.form-container {
  margin-top: 20;
}

.label {
  color: #ffffff;
  margin-top: 10;
  margin-bottom: 5;
}
.mds {
  width: 24;
  height: 24;
}

.input {
  font-size: 14;
  background-color: white;
  color: #36454f;
  margin-bottom: 10;
  height: 50;
  padding-left: 10;
  padding-right: 10;
  border-radius: 2; /* Rounded corners */
  border-width: 0;
  border-color: #e0e0e0;
}

.button {
  background-color: #f6c019;
  color: black;
  margin-top: 20;
  border-radius: 14;
  height: 50;
}

.error-container {
  margin-top: 10;
}

.error-icon {
  color: #ff3b30;
  font-size: 16;
  margin-right: 5;
}

.error-message {
  color: #ff3b30;
  font-size: 14;
}
</style>
