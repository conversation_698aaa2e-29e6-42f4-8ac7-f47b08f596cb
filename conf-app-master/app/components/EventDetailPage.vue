<template>
  <Page>
    <ActionBar>
      <GridLayout columns="auto, *, auto">
        <Label
          @tap="goBack"
          text.decode="&#xe5cb;"
          class="mds chevron-left"
          col="0"
        />
        <Label text="Session Details" class="title" col="1" />
        <Label col="2" />
      </GridLayout>
    </ActionBar>
    <ScrollView>
      <StackLayout class="event-details">
        <Label :text="event.name" class="event-name" textWrap="true" />
        <FlexboxLayout class="info-row">
          <Label text.decode="&#xe916;" class="mds icon" />
          <Label :text="event.date" class="info-text" />
        </FlexboxLayout>
        <FlexboxLayout class="info-row">
          <Label text.decode="&#xe0c8;" class="mds icon" />
          <Label :text="event.venue" class="info-text" />
        </FlexboxLayout>
        <FlexboxLayout class="info-row">
          <Label text.decode="&#xe8b5;" class="mds icon" />
          <Label :text="formatEventTime(event.start_time, event.end_time)" class="info-text" />
        </FlexboxLayout>
        
        <Label text="Speakers" class="section-title" v-if="event.attendee_events.length > 0" />
    <ListView for="attendeeEvent in event.attendee_events" class="speaker-list" v-if="event.attendee_events.length > 0">
      <v-template>
        <GridLayout columns="auto, *, auto" rows="auto, auto" class="speaker-item" @tap="onSpeakerTap(attendeeEvent.attendees)">
          <Image :src="getSpeakerImage(attendeeEvent.attendees)" rowSpan="2" col="0" class="speaker-image" />
          <Label :text="attendeeEvent.attendees.fullname" row="0" col="1" class="speaker-name" />
          <Label :text="attendeeEvent.attendees.organization" row="1" col="1" class="speaker-org" />
          <Label text.decode="&#xe5cc;" class="mds chevron-right" rowSpan="2" col="2" />
        </GridLayout>
      </v-template>
    </ListView>
    <Label text="No speakers assigned yet" class="no-speakers" v-else />
      </StackLayout>
    </ScrollView>
  </Page>
</template>

<script lang="ts">
import Vue from "nativescript-vue";

export default Vue.extend({
  props: {
    event: {
      type: Object,
      required: true,
    },
  },
  methods: {
    goBack() {
      this.$navigateBack();
    },
    formatEventTime(startTime: string, endTime: string): string {
      // Implement time formatting logic here
      return `${startTime} - ${endTime}`;
    },
    getSpeakerImage(speaker: any): string {
      // If you have a default image path, use it here
      return speaker.image || '~/assets/user-placeholder.png';
    },
    onSpeakerTap(speaker: any) {
    // Navigate to speaker details or show more information
    console.log('Speaker tapped:', speaker);
    // this.$navigate to speaker details page
  },
  },
});
</script>

<style scoped lang="scss">
.mds {
  font-family: "Material Symbols Outlined", "MaterialSymbolsOutlined";
  font-weight: normal;
}

.chevron-left {
  font-size: 24;
  color: white;
  margin-left: 10;
}

ActionBar {
  background-color: #262753;
  color: white;
}

.title {
  text-align: center;
  font-size: 20;
  color: white;
  vertical-align: middle;
}

.event-details {
  padding: 20;
}

.event-name {
  font-size: 24;
  // font-weight: bold;
  margin-bottom: 15;
  color: #262753;
}

.info-row {
  margin-bottom: 10;
}

.icon {
  font-size: 20;
  color: #f6c019;
  margin-right: 10;
}

.info-text {
  font-size: 16;
  color: #333;
}

.section-title {
  font-size: 20;
  // font-weight: bold;
  margin-top: 20;
  margin-bottom: 10;
  color: #262753;
}

.speaker-list {
  margin-top: 10;
}

.speaker-item {
  padding: 10;
  margin-bottom: 10;
  background-color: #f8f8f8;
  border-radius: 5;
}

.speaker-image {
  width: 50;
  height: 50;
  border-radius: 25;
  margin-right: 10;
}

.speaker-name {
  font-size: 16;
  font-weight: bold;
  color: #262753;
}

.speaker-org {
  font-size: 14;
  color: #666;
}

.chevron-right {
  font-size: 24;
  color: #999;
}
</style>