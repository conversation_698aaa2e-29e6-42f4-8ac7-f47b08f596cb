<template>
  <StackLayout>
    <Label
      text="Event Agenda"
      textAlignment="center"
      class="action-bar-label"
    />
    <ListView
      for="item in events"
      @itemTap="goToSessionDetailPage"
      height="100%"
    >
      <v-template>
        <GridLayout columns="*, auto" rows="auto, auto, auto" class="list-item">
          <StackLayout col="0" row="0" rowSpan="3">
            <Label textWrap="true" :text="item.name" class="title" />
            <FlexboxLayout
              flexDirection="row"
              flexWrap="wrap"
              alignItems="center"
            >
              <Label text="calendar_today" class="mds icon" color="#f6c019" />
              <Label :text="item.date" class="info-text" />
              <Label text="location_on" class="mds icon" color="#f6c019" />
              <Label :text="item.venue" class="info-text" />
              <Label text="access_time" class="mds icon" color="#f6c019" />
              <Label
                :text="formatEventTime(item.start_time, item.end_time)"
                class="info-text"
              />
            </FlexboxLayout>
            <FlexboxLayout flexDirection="row" alignItems="center">
              <Label text="people" class="mds icon" color="#f6c019" />
              <Label
                :text="listSpeakers(item.attendee_events)"
                textWrap="true"
                class="speakers-text"
              />
            </FlexboxLayout>
          </StackLayout>
          <Label
            text.decode="&#xE5CC;"
            class="mds chevron-right"
            col="1"
            rowSpan="3"
            verticalAlignment="center"
          />
        </GridLayout>
      </v-template>
    </ListView>
  </StackLayout>
</template>

<script lang="ts">
import Vue from "nativescript-vue";
import EventDetailPage from "./EventDetailPage.vue";
import { ItemEventData } from "@nativescript/core";

interface Attendee {
  id: number;
  fullname: string;
  organization: string;
}

interface SpeakerEvent {
  attendees: Attendee;
  attendee_id: number;
  is_speaker_for_event: number;
}

type SpeakerEvents = SpeakerEvent[];

export default Vue.extend({
  props: {
    events: {
      type: Array,
      required: true,
    },
  },
  methods: {
    formatEventTime(start_time: string, end_time: string){
      let start_time_trimmed =""
      if(start_time.endsWith(":00")){
        start_time_trimmed = start_time.slice(0, -3)
      }
      let end_time_trimmed =""
      if(end_time.endsWith(":00")){
        end_time_trimmed = end_time.slice(0, -3)
      }
      return `${start_time_trimmed} - ${end_time_trimmed}`
    },
    goToSessionDetailPage(event: ItemEventData) {
      const selectedEvent = this.events[event.index];
      this.$navigateTo(EventDetailPage, { props: { event: selectedEvent } });
    },
    listSpeakers(speakers: SpeakerEvents) {
      return speakers.map((item) => `${item.attendees.fullname} (${item.attendees.organization})`).join(", ");
    },
  },
});
</script>

<style scoped>
.mds {
  font-family: "Material Symbols Outlined", "MaterialSymbolsOutlined";
  font-weight: 100;
}
.action-bar-label {
  background-color: #262753;
  color: white;
  font-size: 20;
  padding: 10;
  margin: 0;
  height: 56;
  vertical-align: center;
}

.list-item {
  padding: 10;
}

.title {
  font-size: 14;
  margin-bottom: 3;
}

.info-text {
  font-size: 12;
  color: #666;
  margin-right: 0;
}

.speakers-text {
  font-size: 14;
  color: #333;
  margin-top: 1;
}

.chevron-right {
  font-size: 24;
  color: #999;
}
</style>
  organization: any;