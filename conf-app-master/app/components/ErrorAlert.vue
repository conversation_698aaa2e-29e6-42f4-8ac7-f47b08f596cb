<template>
  <GridLayout rows="auto" columns="*" class="error-alert" v-if="error">
    <GridLayout rows="auto, auto" columns="auto, *">
      <Label
        text.decode="&#xe000;"
        class="mds error-icon"
        row="0"
        col="0"
        rowSpan="2"
      />
      <Label :text="error" class="error-main" row="0" col="1" />
      <Label
        :text="description"
        class="error-description"
        row="1"
        col="1"
        textWrap="true"
      >

      </Label>
    </GridLayout>
  </GridLayout>
</template>

<script>

export default {
  name: "ErrorAlert",
  props: {
    error: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      default: "",
    },

  },
  
};
</script>

<!-- <style scoped>
 .error-alert {
  border-width: 1;
  border-radius: 4;
  border-color: #D32F2F;
  background-color: #FFCDD2;
  color: #D32F2F;
  padding: 3;
  margin: 10 0; /* Add some vertical margin */
}
  
  .error-icon {
    font-size: 24;
    margin-right: 10;
    vertical-align: middle;
  }
  
  .error-main {
    font-size: 18;
    font-weight: bold;
    /* margin-bottom: 0; */
  }
  
  .error-description {
    font-size: 14;
  }
  
  .error-link {
    color: #D32F2F;
    text-decoration: underline;
  }
  </style> -->
<style scoped>
.error-alert {
  border-width: 1;
  border-radius: 4;
  border-color: #d32f2f;
  background-color: #ffcdd2;
  color: #d32f2f;
  padding: 3;
  margin-bottom: 10;
}

.error-icon {
  font-size: 24;
  margin-left: 5;
  margin-right: 10;
  vertical-align: middle;
}

.error-main {
  font-size: 14;
  font-weight: bold;
}

.error-description {
  font-size: 12;
}

.error-link {
  color: #d32f2f;
  text-decoration: underline;
}
</style>
