import {
  SUPABASE_API_KEY,
  SUPABASE_AUTH_ENDPOINT,
  ATTENDEES_ENDPOINT,
} from "../app.config";

/**
 * checks the status of the email to determine if user needs to register or login
 * @param email
 */
export async function getAttendeeStatus(email: string) {

  const response = await fetch(`${ATTENDEES_ENDPOINT}?email=eq.${email.toLowerCase()}`, {
    method: "GET",
    headers: {
      apikey: SUPABASE_API_KEY,
    },
  });
  if (!response.ok) {
    console.log(response);
    const error = await response.json();
    throw new Error(error.msg || "Email not registered!");
  }

  return response.json();
}

export async function validateRegistration(
  email: string,
  activationCode: string
) {
  console.log(
    `${ATTENDEES_ENDPOINT}?email=eq.${email}&activation_code=eq.${activationCode}`
  );
  const response = await fetch(
    `${ATTENDEES_ENDPOINT}?email=eq.${email}&activation_code=eq.${activationCode}`,
    {
      method: "GET",
      headers: {
        apikey: SUPABASE_API_KEY,
      },
    }
  );
  if (!response.ok) {
    console.log(response);
    const error = await response.json();
    throw new Error(error.msg || "Failed to verify registration");
  }

  return response.json();
}

export async function signUp(email: string, password: string) {
  const response = await fetch(`${SUPABASE_AUTH_ENDPOINT}/signup`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      apikey: SUPABASE_API_KEY,
    },
    body: JSON.stringify({ email, password }),
  });

  if (!response.ok) {
    const error = await response.json();
    console.log(response);
    throw new Error(error.msg || "Failed to sign up");
  }

  return response.json();
}

export async function signIn(email: string, password: string) {
  const response = await fetch(
    `${SUPABASE_AUTH_ENDPOINT}/token?grant_type=password`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        apikey: SUPABASE_API_KEY,
      },
      body: JSON.stringify({ email, password }),
    }
  );

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error_description || "Failed to sign in");
  }

  return response.json();
}

export async function login(email: string, password: string) {
  const response = await fetch(
    `${SUPABASE_AUTH_ENDPOINT}/token?grant_type=password`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        apikey: SUPABASE_API_KEY,
      },
      body: JSON.stringify({ email, password }),
    }
  );

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error_description || "Failed to sign in");
  }

  return response.json();
}
