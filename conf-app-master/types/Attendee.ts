export enum SetupStatus {
    REGISTERED = "REGISTERED",
    VERIFIED = "VERIFIED",
    SETUP = "SETUP",
    LOGGED_IN = "LOGGED_IN"
}

export type SetupStatusOrEmpty = SetupStatus | "";

export interface Attendee {
    id: number;
    setup_status: SetupStatus;
    created_at: string;
    fullname: string;
    phone_number: string | null;
    designation: string | null;
    sector: string | null;
    banking_institution: string | null;
    broker_dealer_institution: string | null;
    regulatory_institution: string | null;
    educational_institution: string | null;
    media_house: string | null;
    sponsor_institution: string | null;
    exhibitor_institution: string | null;
    other_info: string | null;
    conference_expectation: string | null;
    email: string;
    activation_code: string;
    organization: string | null;
    is_speaker: number;
    updated_at: string | null;
}