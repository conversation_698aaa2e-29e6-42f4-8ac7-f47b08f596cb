{"compilerOptions": {"strict": true, "target": "ES2020", "module": "esnext", "moduleResolution": "node", "lib": ["dom", "ESNext"], "sourceMap": true, "noEmitHelpers": true, "importHelpers": true, "baseUrl": ".", "paths": {"~/*": ["app/*"], "@/*": ["app/*"], "*": ["./*", "types/*"]}, "typeRoots": ["types"], "types": ["node"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true}, "include": ["app", "types", "*.ts"], "exclude": ["node_modules", "platforms"]}