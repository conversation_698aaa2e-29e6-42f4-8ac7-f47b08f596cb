# 🚀 Conference App Deployment Guide

## Quick Start

```bash
# Check what's configured for deployment
node deploy-to-production.js config

# Validate environments are accessible
node deploy-to-production.js validate

# Deploy configured components (by default: database + storage, no functions)
node deploy-to-production.js deploy

# Deploy functions only (when ready)
node deploy-to-production.js functions

# Deploy specific function
node deploy-to-production.js functions send-push-notification

# Rollback if something goes wrong
node deploy-to-production.js rollback backup-1751069234567.json
```

## What the Script Does

### ✅ Configurable Deployment
- **Database Migration**: All tables (speakers, events, event_speakers, etc.)
- **Storage Assets**: Speaker images and other files  
- **Edge Functions**: Supabase functions (optional - deploy when ready)
- **Environment Management**: Handles local → production seamlessly
- **Backup & Rollback**: Creates backups before deployment
- **Validation**: Tests connections before starting

### ⚙️ Configuration Control
Edit `deploy-config.js` to control what gets deployed:
```javascript
deploy: {
  database: true,      // ✅ Tables and data
  storage: true,       // ✅ Buckets and assets  
  functions: false,    // ❌ Skip functions (until ready)
  migrations: false    // ❌ Skip migrations
}
```

### 🔄 Migration Process
1. **Validates** both local and production connections
2. **Backs up** existing production data to timestamped file
3. **Migrates tables** in dependency order (speakers → events → junctions)
4. **Creates storage buckets** with proper permissions
5. **Uploads assets** with correct MIME types
6. **Reports status** throughout the process

### 📁 What Gets Migrated
- **Tables**: speakers, events, event_speakers, user_event_registrations, notification_logs
- **Storage**: speaker-images bucket with all profile pictures
- **Configuration**: Bucket permissions, file size limits, MIME types

## Usage Examples

### Full Deployment
```bash
node deploy-to-production.js deploy
```
Output includes:
- Environment validation ✅
- Backup creation 📋
- Table-by-table migration 📊
- Storage setup 📁
- Success confirmation 🎉

### Environment Check
```bash
node deploy-to-production.js validate
```
Tests both local and production connections.

### Rollback
```bash
node deploy-to-production.js rollback backup-1751069234567.json
```
Restores production to the backup state.

## File Structure
```
aci-conference/
├── deploy-to-production.js    # Main deployment script
├── DEPLOYMENT.md             # This guide
├── old.storage/             # Local assets to migrate
│   └── profile-pics/        # Speaker images
└── backup-*.json           # Auto-generated backups
```

## Configuration

The script includes environment configs for:
- **Local**: `http://*************:54321` (your dev instance)
- **Production**: `https://nyoqbtbakmgbnuhovtcn.supabase.co`

## Safety Features

- 🛡️ **Automatic backups** before any changes
- 🔍 **Environment validation** before deployment  
- 📋 **Detailed logging** with timestamps
- 🔄 **Easy rollback** using backup files
- ⚠️ **Error handling** with meaningful messages

## Benefits

✅ **No more manual migration**  
✅ **Consistent deployments**  
✅ **Safe with rollback capability**  
✅ **Handles all assets (data + images)**  
✅ **Production-ready workflow**

---

**Next Steps**: Run `node deploy-to-production.js validate` to ensure everything is working, then use `deploy` when you need to push changes to production.