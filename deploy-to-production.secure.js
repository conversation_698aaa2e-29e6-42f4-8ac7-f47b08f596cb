#!/usr/bin/env node

/**
 * Automated Production Deployment Script (Secure Version)
 * 
 * This script handles complete migration from local development to production.
 * Credentials are loaded from environment variables for security.
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const config = require('./deploy-config');

// Environment configurations (from environment variables)
const ENVIRONMENTS = {
  local: {
    url: process.env.LOCAL_SUPABASE_URL || 'http://127.0.0.1:54321',
    anonKey: process.env.LOCAL_SUPABASE_ANON_KEY,
    serviceKey: process.env.LOCAL_SUPABASE_SERVICE_KEY
  },
  production: {
    url: process.env.PROD_SUPABASE_URL,
    anonKey: process.env.PROD_SUPABASE_ANON_KEY,
    serviceKey: process.env.PROD_SUPABASE_SERVICE_KEY
  }
};

// Validate environment variables
function validateEnvironment() {
  const required = [
    'LOCAL_SUPABASE_ANON_KEY',
    'LOCAL_SUPABASE_SERVICE_KEY', 
    'PROD_SUPABASE_URL',
    'PROD_SUPABASE_ANON_KEY',
    'PROD_SUPABASE_SERVICE_KEY'
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach(key => console.error(`   - ${key}`));
    console.error('\n💡 Create a .env file with your credentials. See .env.deploy for template.');
    process.exit(1);
  }
}

// Get configuration
const TABLES_TO_MIGRATE = config.tables;
const STORAGE_BUCKETS = config.storage;
const EDGE_FUNCTIONS = config.functions;

class DeploymentManager {
  constructor() {
    validateEnvironment();
    this.localClient = createClient(ENVIRONMENTS.local.url, ENVIRONMENTS.local.serviceKey);
    this.prodClient = createClient(ENVIRONMENTS.production.url, ENVIRONMENTS.production.serviceKey);
    this.backupData = {};
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  async validateEnvironments() {
    this.log('Validating environment connections...');
    
    try {
      // Test local connection
      const localTest = await this.localClient.from('events').select('count', { count: 'exact', head: true });
      if (localTest.error) throw new Error(`Local connection failed: ${localTest.error.message}`);
      this.log(`Local: ${localTest.count} events found`, 'success');

      // Test production connection
      const prodTest = await this.prodClient.from('events').select('count', { count: 'exact', head: true });
      if (prodTest.error) throw new Error(`Production connection failed: ${prodTest.error.message}`);
      this.log(`Production: ${prodTest.count} events found`, 'success');

      return true;
    } catch (error) {
      this.log(`Environment validation failed: ${error.message}`, 'error');
      return false;
    }
  }

  async backupProductionData() {
    this.log('Creating production backup...');
    
    for (const table of TABLES_TO_MIGRATE) {
      try {
        const { data, error } = await this.prodClient.from(table).select('*');
        if (error && !error.message.includes('relation') && !error.message.includes('does not exist')) {
          throw error;
        }
        this.backupData[table] = data || [];
        this.log(`Backed up ${this.backupData[table].length} records from ${table}`);
      } catch (error) {
        this.log(`Warning: Could not backup ${table}: ${error.message}`, 'warning');
        this.backupData[table] = [];
      }
    }

    // Save backup to file
    const backupFile = `backup-${Date.now()}.json`;
    fs.writeFileSync(backupFile, JSON.stringify(this.backupData, null, 2));
    this.log(`Backup saved to ${backupFile}`, 'success');
    return backupFile;
  }

  async migrateTable(tableName) {
    this.log(`Migrating table: ${tableName}`);
    
    try {
      // Get data from local
      const { data: localData, error: localError } = await this.localClient.from(tableName).select('*');
      if (localError) throw localError;

      if (!localData || localData.length === 0) {
        this.log(`No data found in local ${tableName}`, 'warning');
        return;
      }

      // Clear production table (optional - comment out for append-only)
      const { error: deleteError } = await this.prodClient.from(tableName).delete().neq('id', '00000000-0000-0000-0000-000000000000');
      if (deleteError && !deleteError.message.includes('relation') && !deleteError.message.includes('does not exist')) {
        this.log(`Warning: Could not clear production ${tableName}: ${deleteError.message}`, 'warning');
      }

      // Insert data into production
      const { data: insertData, error: insertError } = await this.prodClient
        .from(tableName)
        .insert(localData);

      if (insertError) throw insertError;

      this.log(`Successfully migrated ${localData.length} records to ${tableName}`, 'success');
    } catch (error) {
      this.log(`Failed to migrate ${tableName}: ${error.message}`, 'error');
      throw error;
    }
  }

  async createStorageBucket(bucket) {
    this.log(`Creating storage bucket: ${bucket.name}`);
    
    try {
      const { data, error } = await this.prodClient.storage.createBucket(bucket.name, bucket.config);
      if (error && !error.message.includes('already exists')) {
        throw error;
      }
      this.log(`Storage bucket ${bucket.name} ready`, 'success');
    } catch (error) {
      this.log(`Failed to create bucket ${bucket.name}: ${error.message}`, 'error');
      throw error;
    }
  }

  async migrateStorageAssets(bucket) {
    this.log(`Migrating assets to bucket: ${bucket.name}`);
    
    if (!fs.existsSync(bucket.sourceDir)) {
      this.log(`Source directory not found: ${bucket.sourceDir}`, 'warning');
      return;
    }

    try {
      const files = fs.readdirSync(bucket.sourceDir);
      let uploaded = 0;

      for (const file of files) {
        const filePath = path.join(bucket.sourceDir, file);
        const fileContent = fs.readFileSync(filePath);
        
        const { data, error } = await this.prodClient.storage
          .from(bucket.name)
          .upload(file, fileContent, {
            cacheControl: '3600',
            upsert: true, // Overwrite existing files
            contentType: this.getContentType(file)
          });

        if (error) {
          this.log(`Failed to upload ${file}: ${error.message}`, 'error');
        } else {
          uploaded++;
        }
      }

      this.log(`Storage migration complete: ${uploaded} uploaded`, 'success');
    } catch (error) {
      this.log(`Storage migration failed: ${error.message}`, 'error');
      throw error;
    }
  }

  getContentType(filename) {
    const ext = path.extname(filename).toLowerCase();
    const mimeTypes = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.webp': 'image/webp',
      '.gif': 'image/gif'
    };
    return mimeTypes[ext] || 'application/octet-stream';
  }

  async runCommand(command, args, options = {}) {
    return new Promise((resolve, reject) => {
      const proc = spawn(command, args, { 
        stdio: 'pipe',
        cwd: options.cwd || process.cwd(),
        ...options 
      });

      let stdout = '';
      let stderr = '';

      proc.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      proc.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      proc.on('close', (code) => {
        if (code === 0) {
          resolve({ stdout, stderr });
        } else {
          reject(new Error(`Command failed with code ${code}: ${stderr}`));
        }
      });
    });
  }

  async deployFunctions() {
    if (!config.deploy.functions) {
      this.log('Functions deployment disabled in config', 'warning');
      return;
    }

    this.log('🔧 Deploying Edge Functions...');
    
    try {
      // Check if supabase CLI is available
      await this.runCommand('supabase', ['--version']);
      
      // Deploy functions to production
      const projectRef = ENVIRONMENTS.production.url.split('//')[1].split('.')[0];
      const { stdout } = await this.runCommand('supabase', [
        'functions', 'deploy', 
        '--project-ref', projectRef
      ], { cwd: config.paths.supabaseDir });

      this.log('Functions deployed successfully:', 'success');
      this.log(stdout);
      
    } catch (error) {
      if (error.message.includes('command not found') || error.message.includes('not recognized')) {
        this.log('Supabase CLI not found. Install with: npm install -g supabase', 'warning');
        this.log('Skipping functions deployment...', 'warning');
      } else {
        this.log(`Functions deployment failed: ${error.message}`, 'error');
        throw error;
      }
    }
  }

  async deploySpecificFunctions(functionNames) {
    if (!config.deploy.functions) {
      this.log('Functions deployment disabled in config', 'warning');
      return;
    }

    this.log(`🔧 Deploying specific functions: ${functionNames.join(', ')}`);
    
    try {
      const projectRef = ENVIRONMENTS.production.url.split('//')[1].split('.')[0];
      
      for (const funcName of functionNames) {
        const { stdout } = await this.runCommand('supabase', [
          'functions', 'deploy', funcName,
          '--project-ref', projectRef
        ], { cwd: config.paths.supabaseDir });

        this.log(`✅ ${funcName} deployed`);
      }
      
    } catch (error) {
      this.log(`Functions deployment failed: ${error.message}`, 'error');
      throw error;
    }
  }

  async deploy() {
    try {
      this.log('🚀 Starting production deployment...');
      
      // Step 1: Validate environments
      if (!(await this.validateEnvironments())) {
        throw new Error('Environment validation failed');
      }

      // Step 2: Create backup
      const backupFile = await this.backupProductionData();

      // Step 3: Migrate data
      if (config.deploy.database) {
        this.log('📊 Migrating database tables...');
        for (const table of TABLES_TO_MIGRATE) {
          await this.migrateTable(table);
        }
      } else {
        this.log('📊 Database migration disabled in config', 'warning');
      }

      // Step 4: Setup storage
      if (config.deploy.storage) {
        this.log('📁 Setting up storage buckets...');
        for (const bucket of STORAGE_BUCKETS) {
          await this.createStorageBucket(bucket);
          await this.migrateStorageAssets(bucket);
        }
      } else {
        this.log('📁 Storage migration disabled in config', 'warning');
      }

      // Step 5: Deploy functions
      if (config.deploy.functions) {
        await this.deployFunctions();
      }

      this.log('🎉 Deployment completed successfully!', 'success');
      this.log(`📋 Backup file: ${backupFile}`);
      
      return { success: true, backupFile };
      
    } catch (error) {
      this.log(`💥 Deployment failed: ${error.message}`, 'error');
      this.log('💡 Use the backup file to rollback if needed', 'warning');
      return { success: false, error: error.message };
    }
  }

  async rollback(backupFile) {
    this.log(`🔄 Rolling back from backup: ${backupFile}`);
    
    try {
      const backupData = JSON.parse(fs.readFileSync(backupFile, 'utf8'));
      
      for (const [table, data] of Object.entries(backupData)) {
        if (data.length > 0) {
          // Clear table
          await this.prodClient.from(table).delete().neq('id', '00000000-0000-0000-0000-000000000000');
          
          // Restore data
          const { error } = await this.prodClient.from(table).insert(data);
          if (error) throw error;
          
          this.log(`Restored ${data.length} records to ${table}`, 'success');
        }
      }
      
      this.log('🎉 Rollback completed successfully!', 'success');
    } catch (error) {
      this.log(`💥 Rollback failed: ${error.message}`, 'error');
    }
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  const deployment = new DeploymentManager();
  
  switch (command) {
    case 'deploy':
      await deployment.deploy();
      break;
      
    case 'rollback':
      const backupFile = args[1];
      if (!backupFile) {
        console.log('Usage: node deploy-to-production.secure.js rollback <backup-file>');
        process.exit(1);
      }
      await deployment.rollback(backupFile);
      break;
      
    case 'validate':
      await deployment.validateEnvironments();
      break;

    case 'functions':
      const functionNames = args.slice(1);
      if (functionNames.length > 0) {
        await deployment.deploySpecificFunctions(functionNames);
      } else {
        await deployment.deployFunctions();
      }
      break;

    case 'config':
      console.log('Current deployment configuration:');
      console.log(JSON.stringify(config, null, 2));
      break;
      
    default:
      console.log(`
🚀 Conference App Deployment Tool (Secure Version)

Usage:
  node deploy-to-production.secure.js deploy              - Deploy configured components
  node deploy-to-production.secure.js functions           - Deploy all functions only
  node deploy-to-production.secure.js functions <names>   - Deploy specific functions
  node deploy-to-production.secure.js rollback <file>     - Rollback using backup
  node deploy-to-production.secure.js validate            - Test environment connections
  node deploy-to-production.secure.js config              - Show current configuration

Setup:
  1. Copy .env.deploy to .env
  2. Fill in your actual credentials
  3. Run deploy command

Examples:
  node deploy-to-production.secure.js deploy
  node deploy-to-production.secure.js functions send-push-notification
  node deploy-to-production.secure.js config
      `);
      break;
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { DeploymentManager, ENVIRONMENTS };