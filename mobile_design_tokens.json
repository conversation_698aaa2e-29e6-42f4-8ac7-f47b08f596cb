{"designTokens": {"colors": {"light": {"primary": {"main": "#262755", "light": "#3d3d6b", "dark": "#1a1a3c", "contrast": "#ffffff"}, "secondary": {"main": "#F6BF1A", "light": "#f7c940", "dark": "#d4a312", "contrast": "#000000"}, "background": {"primary": "#ffffff", "secondary": "#f8f9fa", "tertiary": "#f1f3f4"}, "surface": {"primary": "#ffffff", "secondary": "#f8f9fa", "elevated": "#ffffff", "card": "#ffffff"}, "text": {"primary": "#1a1a1a", "secondary": "#6b7280", "tertiary": "#9ca3af", "onPrimary": "#ffffff", "onSecondary": "#000000", "disabled": "#d1d5db"}, "border": {"primary": "#e5e7eb", "secondary": "#f3f4f6", "focus": "#262755"}, "status": {"success": "#10b981", "warning": "#f59e0b", "error": "#ef4444", "info": "#3b82f6"}}, "dark": {"primary": {"main": "#4c4f82", "light": "#6366a3", "dark": "#262755", "contrast": "#ffffff"}, "secondary": {"main": "#F6BF1A", "light": "#f7c940", "dark": "#d4a312", "contrast": "#000000"}, "background": {"primary": "#0f0f0f", "secondary": "#1a1a1a", "tertiary": "#262626"}, "surface": {"primary": "#1a1a1a", "secondary": "#262626", "elevated": "#2d2d2d", "card": "#1f1f1f"}, "text": {"primary": "#ffffff", "secondary": "#d1d5db", "tertiary": "#9ca3af", "onPrimary": "#ffffff", "onSecondary": "#000000", "disabled": "#6b7280"}, "border": {"primary": "#374151", "secondary": "#2d3748", "focus": "#4c4f82"}, "status": {"success": "#34d399", "warning": "#fbbf24", "error": "#f87171", "info": "#60a5fa"}}}, "typography": {"fontFamilies": {"primary": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif", "secondary": "SF Pro Display, Roboto, sans-serif"}, "fontSizes": {"xs": "12px", "sm": "14px", "base": "16px", "lg": "18px", "xl": "20px", "2xl": "24px", "3xl": "30px", "4xl": "36px"}, "fontWeights": {"light": 300, "regular": 400, "medium": 500, "semibold": 600, "bold": 700}, "lineHeights": {"tight": 1.2, "normal": 1.4, "relaxed": 1.6, "loose": 1.8}, "textStyles": {"h1": {"fontSize": "30px", "fontWeight": 700, "lineHeight": 1.2}, "h2": {"fontSize": "24px", "fontWeight": 600, "lineHeight": 1.3}, "h3": {"fontSize": "20px", "fontWeight": 600, "lineHeight": 1.4}, "body": {"fontSize": "16px", "fontWeight": 400, "lineHeight": 1.5}, "bodySmall": {"fontSize": "14px", "fontWeight": 400, "lineHeight": 1.4}, "caption": {"fontSize": "12px", "fontWeight": 400, "lineHeight": 1.3}, "button": {"fontSize": "16px", "fontWeight": 500, "lineHeight": 1.2}}}, "spacing": {"xs": "4px", "sm": "8px", "md": "16px", "lg": "24px", "xl": "32px", "2xl": "48px", "3xl": "64px"}, "borderRadius": {"none": "0px", "sm": "4px", "md": "8px", "lg": "12px", "xl": "16px", "2xl": "24px", "full": "9999px"}, "shadows": {"none": "none", "sm": "0 1px 2px 0 rgba(0, 0, 0, 0.05)", "md": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)", "lg": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)", "xl": "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"}, "components": {"statusBar": {"height": "44px", "backgroundColor": "transparent"}, "navigationBar": {"height": "56px", "backgroundColor": "colors.background.primary", "titleColor": "colors.text.primary", "iconColor": "colors.text.primary", "borderColor": "colors.border.primary"}, "searchBar": {"height": "40px", "backgroundColor": "colors.background.secondary", "borderRadius": "borderRadius.lg", "placeholderColor": "colors.text.tertiary", "iconColor": "colors.text.secondary"}, "card": {"backgroundColor": "colors.surface.card", "borderRadius": "borderRadius.lg", "shadow": "shadows.md", "padding": "spacing.md", "borderColor": "colors.border.primary"}, "primaryCard": {"backgroundColor": "colors.primary.main", "borderRadius": "borderRadius.lg", "textColor": "colors.primary.contrast", "padding": "spacing.md"}, "listItem": {"height": "64px", "backgroundColor": "colors.surface.primary", "borderColor": "colors.border.secondary", "padding": "spacing.md", "avatarSize": "40px"}, "button": {"primary": {"backgroundColor": "colors.primary.main", "textColor": "colors.primary.contrast", "borderRadius": "borderRadius.md", "padding": "12px 24px", "fontSize": "typography.textStyles.button.fontSize", "fontWeight": "typography.textStyles.button.fontWeight"}, "secondary": {"backgroundColor": "colors.surface.primary", "textColor": "colors.primary.main", "borderColor": "colors.border.primary", "borderWidth": "1px", "borderRadius": "borderRadius.md", "padding": "12px 24px"}, "ghost": {"backgroundColor": "transparent", "textColor": "colors.primary.main", "borderRadius": "borderRadius.md", "padding": "12px 24px"}}, "input": {"backgroundColor": "colors.background.secondary", "borderColor": "colors.border.primary", "borderRadius": "borderRadius.md", "padding": "12px 16px", "fontSize": "typography.fontSizes.base", "placeholderColor": "colors.text.tertiary", "focusBorderColor": "colors.border.focus"}, "avatar": {"sizes": {"sm": "32px", "md": "40px", "lg": "48px", "xl": "64px", "2xl": "80px"}, "borderRadius": "borderRadius.full"}, "badge": {"backgroundColor": "colors.secondary.main", "textColor": "colors.secondary.contrast", "borderRadius": "borderRadius.full", "padding": "4px 8px", "fontSize": "typography.fontSizes.xs", "fontWeight": "typography.fontWeights.medium"}, "toggle": {"activeColor": "colors.primary.main", "inactiveColor": "colors.border.primary", "thumbColor": "colors.surface.primary"}, "tabBar": {"height": "80px", "backgroundColor": "colors.surface.primary", "borderColor": "colors.border.primary", "activeColor": "colors.primary.main", "inactiveColor": "colors.text.tertiary", "iconSize": "24px"}, "dateChip": {"backgroundColor": "colors.background.secondary", "selectedBackgroundColor": "colors.primary.main", "textColor": "colors.text.primary", "selectedTextColor": "colors.primary.contrast", "borderRadius": "borderRadius.full", "padding": "8px 12px", "fontSize": "typography.fontSizes.sm"}}, "layout": {"screenPadding": "spacing.md", "sectionSpacing": "spacing.lg", "gridGap": "spacing.md", "maxWidth": "428px"}, "animation": {"duration": {"fast": "150ms", "normal": "250ms", "slow": "350ms"}, "easing": {"ease": "cubic-bezier(0.4, 0, 0.2, 1)", "easeIn": "cubic-bezier(0.4, 0, 1, 1)", "easeOut": "cubic-bezier(0, 0, 0.2, 1)", "easeInOut": "cubic-bezier(0.4, 0, 0.2, 1)"}}}}