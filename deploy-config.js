/**
 * Deployment Configuration
 * 
 * Configure what gets deployed to production
 */

module.exports = {
  // What to deploy (set to false to skip)
  deploy: {
    database: true,      // Tables and data
    storage: true,       // Buckets and assets  
    functions: false,    // Edge Functions (set to true when ready)
    migrations: false    // Database migrations (set to true when ready)
  },

  // Tables to migrate (in dependency order)
  tables: [
    'speakers',
    'events', 
    'event_speakers',
    'user_event_registrations',
    'notification_logs'
  ],

  // Storage buckets to migrate
  storage: [
    {
      name: 'speaker-images',
      public: true,
      config: {
        fileSizeLimit: 1024 * 1024 * 5, // 5MB
        allowedMimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
      },
      sourceDir: '/home/<USER>/Projects/aci-conference/old.storage/profile-pics/'
    }
  ],

  // Edge Functions to deploy (when functions: true)
  functions: [
    'send-push-notification',
    'handle-registration-changes', 
    'handle-schedule-changes'
  ],

  // Supabase project paths
  paths: {
    supabaseDir: '/home/<USER>/Projects/aci-conference/supabase',
    functionsDir: '/home/<USER>/Projects/aci-conference/supabase/functions',
    migrationsDir: '/home/<USER>/Projects/aci-conference/supabase/migrations'
  }
};