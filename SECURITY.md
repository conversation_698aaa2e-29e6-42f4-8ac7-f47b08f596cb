# 🔐 Security Guide

## ⚠️ IMPORTANT: Credentials Security

**NEVER commit credentials to Git!** This repo contains secure deployment scripts that protect your sensitive data.

## 🛡️ Safe for GitHub

The following files are **safe to push** to GitHub:
- ✅ `deploy-to-production.secure.js` - Uses environment variables
- ✅ `deploy-config.js` - No credentials, just configuration
- ✅ `.env.deploy` - Template file only
- ✅ `.gitignore` - Blocks credential files

## 🚫 Blocked from Git

These files are **blocked by .gitignore**:
- ❌ `deploy-to-production.js` - Contains hardcoded credentials  
- ❌ `uploadSpeakerImages.js` - Contains hardcoded credentials
- ❌ `.env` - Your actual credentials
- ❌ `backup-*.json` - May contain sensitive data

## 🔧 Setup for Team Members

When someone clones this repo, they need to:

1. **Copy template**:
   ```bash
   cp .env.deploy .env
   ```

2. **Fill in credentials** in `.env`:
   ```bash
   # Local Development
   LOCAL_SUPABASE_URL=http://127.0.0.1:54321
   LOCAL_SUPABASE_ANON_KEY=your-actual-local-key
   LOCAL_SUPABASE_SERVICE_KEY=your-actual-local-service-key

   # Production
   PROD_SUPABASE_URL=https://your-project-ref.supabase.co
   PROD_SUPABASE_ANON_KEY=your-actual-prod-key
   PROD_SUPABASE_SERVICE_KEY=your-actual-prod-service-key
   ```

3. **Use secure script**:
   ```bash
   node deploy-to-production.secure.js deploy
   ```

## 🎯 Current Status

- ✅ **Credentials secured** with environment variables
- ✅ **Git protection** with .gitignore  
- ✅ **Template provided** for team setup
- ✅ **Secure deployment** workflow ready

## 🚀 Ready to Push

You can now safely push to GitHub! All sensitive credentials are protected.

```bash
git add .
git commit -m "Add secure deployment pipeline"
git push
```