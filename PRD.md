---

## 5. Supabase Configuration & Integration

### 5.1 Database Schema

**Core Tables:**
```sql
-- Users table (extends auth.users)
CREATE TABLE profiles (
  id UUID REFERENCES auth.users NOT NULL PRIMARY KEY,
  full_name TEXT,
  job_title TEXT,
  company TEXT,
  bio TEXT,
  avatar_url TEXT,
  linkedin_url TEXT,
  twitter_url TEXT,
  fcm_token TEXT,
  role TEXT DEFAULT 'attendee' CHECK (role IN ('attendee', 'speaker', 'organizer', 'admin')),
  notification_settings JSONB DEFAULT '{}',
  privacy_settings JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Events table
CREATE TABLE events (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE NOT NULL,
  location TEXT,
  category TEXT,
  image_url TEXT,
  is_virtual BOOLEAN DEFAULT FALSE,
  meeting_url TEXT,
  tags TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Speakers table
CREATE TABLE speakers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  title TEXT,
  bio TEXT,
  image_url TEXT,
  linkedin_url TEXT,
  twitter_url TEXT,
  company TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Event speakers junction table
CREATE TABLE event_speakers (
  event_id UUID REFERENCES events(id) ON DELETE CASCADE,
  speaker_id UUID REFERENCES speakers(id) ON DELETE CASCADE,
  PRIMARY KEY (event_id, speaker_id)
);

-- User favorites
CREATE TABLE user_favorites (
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  event_id UUID REFERENCES events(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (user_id, event_id)
);

-- Chat rooms
CREATE TABLE chat_rooms (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  type TEXT CHECK (type IN ('general', 'event', 'direct')),
  event_id UUID REFERENCES events(id) ON DELETE CASCADE,
  image_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chat room participants
CREATE TABLE chat_participants (
  room_id UUID REFERENCES chat_rooms(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (room_id, user_id)
);

-- Messages
CREATE TABLE messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  room_id UUID REFERENCES chat_rooms(id) ON DELETE CASCADE,
  sender_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT,
  type TEXT CHECK (type IN ('text', 'image', 'system')) DEFAULT 'text',
  image_url TEXT,
  reply_to_id UUID REFERENCES messages(id),
  reactions JSONB DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications
CREATE TABLE notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  message TEXT,
  type TEXT CHECK (type IN ('event_reminder', 'schedule_change', 'chat_message', 'announcement')),
  data JSONB DEFAULT '{}',
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 5.2 Row Level Security (RLS) Policies

```sql
-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view all profiles" ON profiles FOR SELECT USING (true);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- User favorites policies
CREATE POLICY "Users can manage own favorites" ON user_favorites FOR ALL USING (auth.uid() = user_id);

-- Chat participants policies
CREATE POLICY "Users can view rooms they participate in" ON chat_participants FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can join rooms" ON chat_participants FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Messages policies
CREATE POLICY "Users can view messages in their rooms" ON messages FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM chat_participants 
    WHERE room_id = messages.room_id AND user_id = auth.uid()
  )
);
CREATE POLICY "Users can send messages to their rooms" ON messages FOR INSERT WITH CHECK (
  auth.uid() = sender_id AND
  EXISTS (
    SELECT 1 FROM chat_participants 
    WHERE room_id = messages.room_id AND user_id = auth.uid()
  )
);

-- Notifications policies
CREATE POLICY "Users can view own notifications" ON notifications FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own notifications" ON notifications FOR UPDATE USING (auth.uid() = user_id);
```

### 5.3 Supabase Realtime Configuration

**Real-time Subscriptions Setup:**
```sql
-- Enable realtime for required tables
ALTER PUBLICATION supabase_realtime ADD TABLE messages;
ALTER PUBLICATION supabase_realtime ADD TABLE chat_participants;
ALTER PUBLICATION supabase_realtime ADD TABLE notifications;
ALTER PUBLICATION supabase_realtime ADD TABLE user_favorites;
```

**Flutter Realtime Integration:**
```dart
class SupabaseRealtime {
  static Future<void> subscribeToChat(String roomId, Function(Message) onMessage) async {
    supabase
      .from('messages')
      .stream(primaryKey: ['id'])
      .eq('room_id', roomId)
      .listen((data) {
        final message = Message.fromJson(data.last);
        onMessage(message);
      });
  }
  
  static Future<void> subscribeToNotifications(String userId, Function(Notification) onNotification) async {
    supabase
      .from('notifications')
      .stream(primaryKey: ['id'])
      .eq('user_id', userId)
      .listen((data) {
        final notification = Notification.fromJson(data.last);
        onNotification(notification);
      });
  }
  
  static Future<void> subscribeToUserPresence(String roomId) async {
    final channel = supabase.channel('room_$roomId');
    
    channel.on(RealtimeListenTypes.presence, ChannelFilter(event: 'sync'), (payload, [ref]) {
      // Handle user presence updates
    });
    
    await channel.subscribe();
  }
}
```

### 5.4 Supabase Storage Configuration

**Storage Buckets Setup:**
```sql
-- Create storage buckets
INSERT INTO storage.buckets (id, name, public) VALUES 
  ('avatars', 'avatars', true),
  ('event-images', 'event-images', true),
  ('chat-images', 'chat-images', false);

-- Storage policies for avatars
CREATE POLICY "Avatar images are publicly accessible" ON storage.objects 
  FOR SELECT USING (bucket_id = 'avatars');

CREATE POLICY "Users can upload their own avatar" ON storage.objects 
  FOR INSERT WITH CHECK (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can update their own avatar" ON storage.objects 
  FOR UPDATE USING (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Storage policies for event images
CREATE POLICY "Event images are publicly accessible" ON storage.objects 
  FOR SELECT USING (bucket_id = 'event-images');

-- Storage policies for chat images
CREATE POLICY "Chat images accessible to room participants" ON storage.objects 
  FOR SELECT USING (
    bucket_id = 'chat-images' AND 
    EXISTS (
      SELECT 1 FROM chat_participants cp
      JOIN messages m ON m.room_id = cp.room_id
      WHERE cp.user_id = auth.uid() AND m.image_url LIKE '%' || name || '%'
    )
  );

CREATE POLICY "Users can upload chat images" ON storage.objects 
  FOR INSERT WITH CHECK (bucket_id = 'chat-images' AND auth.uid()::text = (storage.foldername(name))[1]);
```

**Flutter Storage Integration:**
```dart
class SupabaseStorageService {
  static Future<String?> uploadAvatar(File imageFile, String userId) async {
    try {
      final fileName = '${userId}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final path = await supabase.storage
          .from('avatars')
          .upload('$userId/$fileName', imageFile);
      
      return supabase.storage.from('avatars').getPublicUrl('$userId/$fileName');
    } catch (e) {
      throw Exception('Failed to upload avatar: $e');
    }
  }
  
  static Future<String?> uploadChatImage(File imageFile, String userId, String roomId) async {
    try {
      final fileName = '${DateTime.now().millisecondsSinceEpoch}.jpg';
      final path = await supabase.storage
          .from('chat-images')
          .upload('$userId/$roomId/$fileName', imageFile);
      
      return supabase.storage.from('chat-images').getPublicUrl('$userId/$roomId/$fileName');
    } catch (e) {
      throw Exception('Failed to upload chat image: $e');
    }
  }
}
```

### 5.5 Supabase Edge Functions

**Push Notification Function:**
```typescript
// supabase/functions/send-notification/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { userId, title, body, data } = await req.json()
    
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    )

    // Get user's FCM token
    const { data: profile, error } = await supabaseClient
      .from('profiles')
      .select('fcm_token')
      .eq('id', userId)
      .single()

    if (error || !profile?.fcm_token) {
      throw new Error('User FCM token not found')
    }

    // Send FCM notification
    const fcmResponse = await fetch('https://fcm.googleapis.com/fcm/send', {
      method: 'POST',
      headers: {
        'Authorization': `key=${Deno.env.get('FCM_SERVER_KEY')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: profile.fcm_token,
        notification: { title, body },
        data: data || {},
      }),
    })

    // Store notification in database
    await supabaseClient
      .from('notifications')
      .insert({
        user_id: userId,
        title,
        message: body,
        type: data?.type || 'general',
        data: data || {},
      })

    return new Response(
      JSON.stringify({ success: true }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } },
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 },
    )
  }
})
```

**Event Reminder Function:**
```typescript
// supabase/functions/event-reminders/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

serve(async (req) => {
  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
  )

  try {
    // Find events starting in 15 minutes
    const fifteenMinutesFromNow = new Date(Date.now() + 15 * 60 * 1000).toISOString()
    const sixteenMinutesFromNow = new Date(Date.now() + 16 * 60 * 1000).toISOString()

    const { data: upcomingEvents, error: eventsError } = await supabaseClient
      .from('events')
      .select(`
        *,
        user_favorites(user_id)
      `)
      .gte('start_time', fifteenMinutesFromNow)
      .lt('start_time', sixteenMinutesFromNow)

    if (eventsError) throw eventsError

    // Send notifications to users who favorited these events
    for (const event of upcomingEvents || []) {
      for (const favorite of event.user_favorites) {
        await supabaseClient.functions.invoke('send-notification', {
          body: {
            userId: favorite.user_id,
            title: 'Event Starting Soon',
            body: `${event.title} starts in 15 minutes`,
            data: {
              type: 'event_reminder',
              eventId: event.id,
              action: 'view_event'
            }
          }
        })
      }
    }

    return new Response(JSON.stringify({ success: true }))
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), { status: 400 })
  }
})
```

### 5.6 Supabase Security Configuration

**Additional RLS Policies:**
```sql
-- Events table policies (public read)
CREATE POLICY "Events are viewable by everyone" ON events FOR SELECT USING (true);
CREATE POLICY "Only admins can modify events" ON events FOR ALL USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() AND (profiles.role = 'admin' OR profiles.role = 'organizer')
  )
);

-- Speakers table policies (public read)
CREATE POLICY "Speakers are viewable by everyone" ON speakers FOR SELECT USING (true);
CREATE POLICY "Only admins can modify speakers" ON speakers FOR ALL USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() AND (profiles.role = 'admin' OR profiles.role = 'organizer')
  )
);

-- Chat rooms policies
CREATE POLICY "Users can view public chat rooms" ON chat_rooms FOR SELECT USING (
  type = 'general' OR 
  EXISTS (
    SELECT 1 FROM chat_participants 
    WHERE room_id = chat_rooms.id AND user_id = auth.uid()
  )
);

CREATE POLICY "Users can create direct message rooms" ON chat_rooms FOR INSERT WITH CHECK (
  type = 'direct' AND 
  auth.uid() IS NOT NULL
);
```

**Database Functions:**
```sql
-- Function to automatically create user profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, full_name, avatar_url)
  VALUES (new.id, new.raw_user_meta_data->>'full_name', new.raw_user_meta_data->>'avatar_url');
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to call the function on user creation
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS trigger AS $$
BEGIN
  new.updated_at = now();
  RETURN new;
END;
$$ LANGUAGE plpgsql;

-- Apply the trigger to relevant tables
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON profiles
  FOR EACH ROW EXECUTE PROCEDURE handle_updated_at();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON events
  FOR EACH ROW EXECUTE PROCEDURE handle_updated_at();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON chat_rooms
  FOR EACH ROW EXECUTE PROCEDURE handle_updated_at();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON messages
  FOR EACH ROW EXECUTE PROCEDURE handle_updated_at();
```

### 5.7 Flutter Supabase Client Setup

**Environment Configuration:**
```dart
// lib/core/config/env_config.dart
class EnvConfig {
  static const String supabaseUrl = String.fromEnvironment(
    'SUPABASE_URL',
    defaultValue: 'https://your-project.supabase.co',
  );
  
  static const String supabaseAnonKey = String.fromEnvironment(
    'SUPABASE_ANON_KEY',
    defaultValue: 'your-anon-key',
  );
}
```

**Main App Initialization:**
```dart
// lib/main.dart
import 'package:supabase_flutter/supabase_flutter.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  await Supabase.initialize(
    url: EnvConfig.supabaseUrl,
    anonKey: EnvConfig.supabaseAnonKey,
    authOptions: const FlutterAuthClientOptions(
      authFlowType: AuthFlowType.pkce,
      autoRefreshToken: true,
    ),
    realtimeClientOptions: const RealtimeClientOptions(
      logLevel: RealtimeLogLevel.info,
      timeout: Duration(seconds: 20),
    ),
  );
  
  runApp(const MyApp());
}

// Global Supabase client instance
final supabase = Supabase.instance.client;
```

**Auth State Management:**
```dart
// lib/features/auth/services/auth_service.dart
class AuthService {
  static Future<AuthResponse> signInWithEmail(String email) async {
    return await supabase.auth.signInWithOtp(
      email: email,
      emailRedirectTo: 'com.yourapp.conference://auth/callback',
    );
  }
  
  static Future<AuthResponse> verifyOTP(String email, String token) async {
    return await supabase.auth.verifyOTP(
      email: email,
      token: token,
      type: OtpType.email,
    );
  }
  
  static Future<void> signOut() async {
    await supabase.auth.signOut();
  }
  
  static User? get currentUser => supabase.auth.currentUser;
  
  static Stream<AuthState> get authStateChanges => 
      supabase.auth.onAuthStateChange;
}
```

**Repository Pattern Implementation:**
```dart
// lib/features/events/repositories/events_repository.dart
class EventsRepository {
  Future<List<Event>> getEvents({
    int page = 1,
    int limit = 20,
    String? search,
    String? category,
  }) async {
    var query = supabase
        .from('events')
        .select('''
          *,
          speakers:event_speakers(
            speaker:speakers(*)
          ),
          user_favorites!left(user_id)
        ''')
        .order('start_time', ascending: true)
        .range((page - 1) * limit, page * limit - 1);

    if (search != null && search.isNotEmpty) {
      query = query.or('title.ilike.%$search%,description.ilike.%$search%');
    }

    if (category != null) {
      query = query.eq('category', category);
    }

    final response = await query;
    return response.map((json) => Event.fromJson(json)).toList();
  }
  
  Future<void> toggleFavorite(String eventId, bool isFavorite) async {
    final userId = supabase.auth.currentUser?.id;
    if (userId == null) throw Exception('User not authenticated');

    if (isFavorite) {
      await supabase.from('user_favorites').insert({
        'user_id': userId,
        'event_id': eventId,
      });
    } else {
      await supabase
          .from('user_favorites')
          .delete()
          .eq('user_id', userId)
          .eq('event_id', eventId);
    }
  }
}
```

---

# Conference App — Expo React Native PRD (LLM-Optimized)

## Project Overview
**Goal:** Rebuild the Vue-based conference app in Expo React Native with feature parity, improved UX, and cross-platform support (iOS/Android).

**Target Users:** Conference attendees, speakers, organizers
**Timeline:** [Define timeline]
**Success Metrics:** User engagement, session attendance tracking, chat activity

---

## 1. Technical Architecture

### 1.1 Tech Stack
- **Framework:** Expo React Native (SDK 50+)
- **Backend:** Supabase (PostgreSQL database, Auth, Storage, Realtime)
- **State Management:** Zustand (lightweight and TypeScript-friendly)
- **Navigation:** Expo Router (file-based routing)
- **HTTP Client:** Supabase client with React Query for caching
- **Local Storage:** Expo SecureStore and AsyncStorage
- **Authentication:** Supabase Auth with Expo AuthSession
- **Push Notifications:** Expo Notifications + Supabase Edge Functions
- **Real-time Chat:** Supabase Realtime (WebSocket-based)
- **File Storage:** Supabase Storage for images and documents
- **Image Handling:** Expo Image for optimized performance
- **Calendar Integration:** Expo Calendar API

### 1.2 Project Structure
```
src/
├── app/
│   ├── (auth)/
│   ├── (tabs)/
│   └── _layout.tsx
├── components/
│   ├── ui/
│   ├── forms/
│   └── chat/
├── services/
│   ├── supabase/
│   ├── notifications/
│   └── storage/
├── stores/
│   ├── auth.ts
│   ├── events.ts
│   └── chat.ts
├── types/
├── utils/
└── constants/
```

### 1.3 Key Dependencies
```json
{
  "dependencies": {
    "expo": "~50.0.0",
    "@expo/vector-icons": "^14.0.0",
    "expo-router": "~3.4.0",
    "expo-image": "~1.10.0",
    "expo-notifications": "~0.27.0",
    "expo-secure-store": "~12.9.0",
    "expo-calendar": "~12.8.0",
    "expo-camera": "~14.1.0",
    "expo-image-picker": "~14.7.0",
    "@supabase/supabase-js": "^2.39.0",
    "@tanstack/react-query": "^5.17.0",
    "zustand": "^4.5.0",
    "react-native-reanimated": "~3.6.2",
    "react-native-gesture-handler": "~2.14.0",
    "react-native-safe-area-context": "4.8.2",
    "react-native-screens": "~3.29.0",
    "@react-native-async-storage/async-storage": "1.21.0",
    "react-hook-form": "^7.49.0",
    "zod": "^3.22.0"
  }
}
```

---

## 2. LLM Development Guidelines

### 2.1 Code Generation Instructions
When generating React Native/Expo code, follow these patterns:

**File Structure Pattern:**
```typescript
// For each feature, create these files:
// - feature-screen.tsx (UI component)
// - feature-store.ts (Zustand store)
// - feature-types.ts (TypeScript types)
// - feature-service.ts (API calls)
// - feature-hooks.ts (React Query hooks)
```

**Store Pattern (Zustand):**
```typescript
// Always use this pattern for state management
interface FeatureState {
  data: DataModel[] | null;
  loading: boolean;
  error: string | null;
  fetchData: () => Promise<void>;
  reset: () => void;
}

export const useFeatureStore = create<FeatureState>((set, get) => ({
  data: null,
  loading: false,
  error: null,
  
  fetchData: async () => {
    set({ loading: true, error: null });
    try {
      const data = await featureService.fetchData();
      set({ data, loading: false });
    } catch (error) {
      set({ error: error.message, loading: false });
    }
  },
  
  reset: () => set({ data: null, loading: false, error: null }),
}));
```

**React Query Pattern:**
```typescript
// Use React Query for server state management
export const useFeatureQuery = () => {
  return useQuery({
    queryKey: ['feature'],
    queryFn: () => featureService.fetchData(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useFeatureMutation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: featureService.updateData,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['feature'] });
    },
  });
};
```

**Component Pattern:**
```typescript
// Use TypeScript interfaces for props
interface FeatureScreenProps {
  navigation: NavigationProp<any>;
}

export default function FeatureScreen({ navigation }: FeatureScreenProps) {
  const { data, loading, error } = useFeatureQuery();
  
  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  
  return (
    <View style={styles.container}>
      {/* Component content */}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
});
```

### 2.2 Code Quality Requirements
- Always include error handling with try-catch blocks
- Use TypeScript for type safety
- Implement proper loading and error states
- Add accessibility props (accessibilityLabel, accessibilityRole)
- Follow React Native and Expo best practices
- Use ESLint and Prettier for code formatting
- Add comprehensive JSDoc comments for complex logic

---

## 3. Detailed User Stories & Acceptance Criteria

### 3.1 Authentication Flow

**Story:** As a conference attendee, I want to securely log in so I can access personalized content.

**Screens & Flow:**
1. **LoginScreen**
   - Input: Email field with validation using react-hook-form
   - Action: Send verification code to email
   - Error states: Invalid email, network errors
   - Success: Navigate to VerificationScreen

2. **VerificationScreen**
   - Input: 6-digit code field with auto-focus
   - Features: Resend code (with cooldown), auto-verification
   - Error states: Invalid code, expired code
   - Success: Navigate to SetupScreen (first time) or main tabs

3. **SetupScreen** (First-time users only)
   - Inputs: Full name, profile picture upload (optional)
   - Validation: Name required, image size limits
   - Success: Complete profile setup, navigate to main app

**Technical Requirements:**
- Supabase Auth integration with email magic links
- Auto-login using Supabase session management with Expo SecureStore
- User profile creation in Supabase database
- Row Level Security (RLS) policies for user data
- Expo LocalAuthentication for biometric authentication (future)

**API Integration:**
```typescript
// Supabase Auth service
export const authService = {
  signInWithOTP: (email: string) => supabase.auth.signInWithOtp({ email }),
  verifyOTP: (email: string, token: string) => 
    supabase.auth.verifyOtp({ email, token, type: 'email' }),
  signOut: () => supabase.auth.signOut(),
  getCurrentSession: () => supabase.auth.getSession(),
};
```

**LLM Implementation Notes:**
- Use Zustand store for auth state management 
- Implement Supabase auth listener for session changes
- Add Expo SecureStore for secure token storage
- Include form validation with react-hook-form and zod
- Add loading indicators and error handling with React Query
- Use Expo Router for navigation between auth screens

### 3.2 Events Management

**Story:** As an attendee, I want to browse and manage conference events to plan my schedule.

**EventsScreen:**
- **Features:**
  - Search bar with real-time filtering
  - Category filters (Keynote, Workshop, Networking)
  - Date/time filters
  - Favorite events toggle
  - Pull-to-refresh with RefreshControl
- **List Item Components:**
  - Event title, time, location
  - Speaker avatars
  - Favorite star icon
  - Attendance indicator
- **States:** Loading, empty, error, populated

**EventDetailScreen:**
- **Content Sections:**
  - Hero image/banner with Expo Image
  - Title, date/time, location
  - Speaker information with bios
  - Event description (rich text)
  - Related documents/links
- **Actions:**
  - Add to calendar using Expo Calendar
  - Set reminder with Expo Notifications
  - Share event with Expo Sharing
  - Join virtual session (if applicable)
- **Interactive Elements:**
  - Speaker profile navigation
  - Location map integration with Expo MapView
  - Q&A section (if enabled)

**Technical Requirements:**
- Offline caching with React Query and AsyncStorage
- Calendar permissions handling with Expo Calendar
- Deep linking to specific events with Expo Router
- Image caching and optimization with Expo Image

**Data Models:**
```typescript
export interface Event {
  id: string;
  title: string;
  description: string;
  start_time: string; // ISO string
  end_time: string; // ISO string
  location: string;
  speakers: Speaker[];
  category: string;
  image_url?: string;
  is_favorite?: boolean;
  is_virtual?: boolean;
  meeting_url?: string;
  tags: string[];
  created_at: string;
  updated_at: string;
}

export interface Speaker {
  id: string;
  name: string;
  title: string;
  bio: string;
  image_url?: string;
  linkedin_url?: string;
  twitter_url?: string;
  company?: string;
}

export interface EventFilters {
  search?: string;
  category?: string;
  startDate?: Date;
  endDate?: Date;
  showFavoritesOnly?: boolean;
}
```

**LLM Implementation Notes:**
- Use TypeScript interfaces for type safety
- Implement search with lodash debounce (300ms)
- Add FlatList with pagination for large event lists
- Include RefreshControl for pull-to-refresh functionality
- Add smooth transitions with React Native Reanimated
- Implement offline-first architecture with React Query

### 3.3 Real-time Chat System

**Story:** As an attendee, I want to network with other participants through chat.

**ChatScreen Features:**
- **Chat Types:**
  - General conference chat
  - Event-specific chat rooms
  - Direct messages
- **Message Features:**
  - Text messages with emoji support
  - Image sharing with Expo ImagePicker
  - Reply to messages
  - Message reactions
  - Online status indicators
- **UI Components:**
  - FlatList for chat messages with optimized rendering
  - Chat list with unread badges
  - Search conversations
  - Message bubbles with timestamps
  - Typing indicators
  - Message status (sent, delivered, read)

**Technical Implementation:**
- Supabase Realtime subscriptions for live chat
- PostgreSQL database with optimized queries
- Message pagination using FlatList with React Query
- Push notifications via Expo Notifications and Supabase Edge Functions
- Image upload to Supabase Storage with automatic optimization
- Connection state handled by Supabase client
- Presence system for online/offline status

**Data Models:**
```typescript
export type ChatType = 'general' | 'event' | 'direct';
export type MessageType = 'text' | 'image' | 'system';
export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'read' | 'failed';

export interface ChatRoom {
  id: string;
  name: string;
  type: ChatType;
  participants: string[];
  last_message?: Message;
  unread_count: number;
  last_activity?: string;
  image_url?: string;
  created_at: string;
  updated_at: string;
}

export interface Message {
  id: string;
  room_id: string;
  sender_id: string;
  content: string;
  type: MessageType;
  timestamp: string;
  image_url?: string;
  reply_to_id?: string;
  reactions: MessageReaction[];
  status: MessageStatus;
  created_at: string;
  updated_at: string;
}

export interface MessageReaction {
  emoji: string;
  user_ids: string[];
}

export interface SendMessageRequest {
  room_id: string;
  content: string;
  type: MessageType;
  image_url?: string;
  reply_to_id?: string;
}
```

**LLM Implementation Notes:**
- Set up Supabase Realtime subscriptions for chat rooms
- Implement message threading with PostgreSQL foreign keys
- Add image upload to Supabase Storage with RLS policies
- Use Supabase presence for typing indicators and online status
- Include message reactions using PostgreSQL JSONB columns
- Add push notifications via Supabase Edge Functions
- Use FlatList with getItemLayout for optimal performance
- Implement message caching with React Query

### 3.4 Profile Management

**ProfileScreen Features:**
- **Editable Fields:**
  - Profile picture (camera/gallery options with Expo ImagePicker)
  - Full name
  - Job title
  - Company
  - Bio
  - Social links (LinkedIn, Twitter)
- **Settings:**
  - Notification preferences
  - Privacy settings
  - Theme selection (light/dark) with Expo appearance
  - Language selection
- **Actions:**
  - View QR code for networking
  - Export contact information with Expo Sharing
  - Logout with confirmation

**Technical Requirements:**
- Image upload to Supabase Storage with RLS policies
- Form validation with react-hook-form and zod
- QR code generation with react-native-qrcode-svg
- Settings stored in Supabase user metadata and AsyncStorage
- Profile data synced with Supabase auth user

**Data Models:**
```typescript
export interface UserProfile {
  id: string;
  email: string;
  full_name?: string;
  job_title?: string;
  company?: string;
  bio?: string;
  avatar_url?: string;
  linkedin_url?: string;
  twitter_url?: string;
  notification_settings: NotificationSettings;
  privacy_settings: PrivacySettings;
  theme_mode: 'light' | 'dark' | 'system';
  language: string;
  created_at: string;
  updated_at: string;
}

export interface NotificationSettings {
  event_reminders: boolean;
  chat_messages: boolean;
  announcements: boolean;
  schedule_changes: boolean;
  quiet_hours_start: string; // HH:MM format
  quiet_hours_end: string; // HH:MM format
}

export interface PrivacySettings {
  profile_visibility: 'public' | 'attendees_only' | 'private';
  show_online_status: boolean;
  allow_direct_messages: boolean;
}

export interface ContactExport {
  name: string;
  title?: string;
  company?: string;
  email: string;
  linkedin?: string;
  twitter?: string;
}
```

**LLM Implementation Notes:**
- Add image upload to Supabase Storage with Expo ImageManipulator for compression
- Implement QR code generation for contact sharing with react-native-qrcode-svg
- Add form validation using react-hook-form with zod schemas
- Store user preferences in Supabase user metadata and AsyncStorage for offline access
- Add data export functionality using Expo FileSystem and Sharing for privacy compliance
- Use Expo ImagePicker with camera and gallery permissions

### 3.5 Push Notifications

**NotificationsScreen Features:**
- **Notification Types:**
  - Event reminders (15 min, 1 hour before)
  - Schedule changes
  - New messages
  - General announcements
- **Interactive Elements:**
  - Mark as read/unread
  - Delete notifications
  - Quick actions (join event, reply to chat)
- **Settings:**
  - Notification preferences per type
  - Quiet hours configuration

**LLM Implementation Notes:**
- Implement push notifications using Expo Notifications and Supabase Edge Functions
- Add local notifications for event reminders with Expo Notifications scheduling
- Include notification action handling with deep links using Expo Router
- Store notification history in Supabase database
- Implement notification badges with real-time updates
- Use Expo Notifications to request permissions and handle device tokens

---

## 4. UI/UX Specifications

### 4.1 Design System

**Color Palette:**
```typescript
export const AppColors = {
  // Primary Colors
  primary: '#1976D2',
  primaryLight: '#63A4FF',
  primaryDark: '#004BA0',
  
  // Secondary Colors
  secondary: '#FF9800',
  secondaryLight: '#FFCC02',
  secondaryDark: '#C66900',
  
  // Neutral Colors
  background: '#F5F5F5',
  surface: '#FFFFFF',
  surfaceVariant: '#F3F3F3',
  
  // Semantic Colors
  error: '#D32F2F',
  success: '#388E3C',
  warning: '#F57C00',
  info: '#1976D2',
  
  // Text Colors
  textPrimary: '#212121',
  textSecondary: '#757575',
  textHint: '#BDBDBD',
  textOnPrimary: '#FFFFFF',
  
  // Chat Colors
  chatBubbleOwn: '#1976D2',
  chatBubbleOther: '#E0E0E0',
  chatBubbleSystem: '#FFF3E0',
  
  // Status Colors
  online: '#4CAF50',
  offline: '#9E9E9E',
  away: '#FF9800',
} as const;
```

**Typography:**
```typescript
export const AppFonts = {
  h1: {
    fontFamily: 'Montserrat-Bold',
    fontSize: 24,
    lineHeight: 29,
    fontWeight: '700' as const,
  },
  h2: {
    fontFamily: 'Montserrat-Bold',
    fontSize: 20,
    lineHeight: 25,
    fontWeight: '700' as const,
  },
  h3: {
    fontFamily: 'Montserrat-SemiBold',
    fontSize: 18,
    lineHeight: 23,
    fontWeight: '600' as const,
  },
  bodyLarge: {
    fontFamily: 'Poppins-Regular',
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '400' as const,
  },
  bodyMedium: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '400' as const,
  },
  caption: {
    fontFamily: 'Poppins-Light',
    fontSize: 12,
    lineHeight: 16,
    fontWeight: '300' as const,
  },
} as const;
```

**Component Specifications:**
```typescript
// Button Styles
export const ButtonStyles = StyleSheet.create({
  primary: {
    backgroundColor: AppColors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  secondary: {
    backgroundColor: 'transparent',
    borderColor: AppColors.primary,
    borderWidth: 1,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  primaryText: {
    color: AppColors.textOnPrimary,
    ...AppFonts.bodyMedium,
    fontWeight: '600',
  },
  secondaryText: {
    color: AppColors.primary,
    ...AppFonts.bodyMedium,
    fontWeight: '600',
  },
});

// Input Field Styles
export const InputStyles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  input: {
    borderWidth: 1,
    borderColor: AppColors.textHint,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: AppColors.textPrimary,
  },
  inputFocused: {
    borderColor: AppColors.primary,
    borderWidth: 2,
  },
  inputError: {
    borderColor: AppColors.error,
  },
  label: {
    ...AppFonts.bodyMedium,
    color: AppColors.textSecondary,
    marginBottom: 8,
  },
  errorText: {
    ...AppFonts.caption,
    color: AppColors.error,
    marginTop: 4,
  },
});
```

### 4.2 Navigation

**Tab Navigation (using Expo Router):**
```typescript
// app/(tabs)/_layout.tsx
export default function TabLayout() {
  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: AppColors.primary,
        tabBarInactiveTintColor: AppColors.textSecondary,
        tabBarStyle: {
          backgroundColor: AppColors.surface,
          borderTopColor: AppColors.surfaceVariant,
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ color, focused }) => (
            <Ionicons 
              name={focused ? 'home' : 'home-outline'} 
              size={24} 
              color={color} 
            />
          ),
        }}
      />
      <Tabs.Screen
        name="events"
        options={{
          title: 'Events',
          tabBarIcon: ({ color, focused }) => (
            <Ionicons 
              name={focused ? 'calendar' : 'calendar-outline'} 
              size={24} 
              color={color} 
            />
          ),
        }}
      />
      <Tabs.Screen
        name="chat"
        options={{
          title: 'Chat',
          tabBarIcon: ({ color, focused }) => (
            <Ionicons 
              name={focused ? 'chatbubbles' : 'chatbubbles-outline'} 
              size={24} 
              color={color} 
            />
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color, focused }) => (
            <Ionicons 
              name={focused ? 'person' : 'person-outline'} 
              size={24} 
              color={color} 
            />
          ),
        }}
      />
      <Tabs.Screen
        name="more"
        options={{
          title: 'More',
          tabBarIcon: ({ color, focused }) => (
            <Ionicons 
              name={focused ? 'menu' : 'menu-outline'} 
              size={24} 
              color={color} 
            />
          ),
        }}
      />
    </Tabs>
  );
}
```

**Deep Linking Structure (Expo Router):**
```typescript
// File-based routing structure
app/
├── (auth)/
│   ├── login.tsx
│   ├── verify.tsx
│   └── setup.tsx
├── (tabs)/
│   ├── index.tsx          // Home tab
│   ├── events/
│   │   ├── index.tsx      // Events list
│   │   └── [id].tsx       // Event detail
│   ├── chat/
│   │   ├── index.tsx      // Chat list
│   │   └── [roomId].tsx   // Chat room
│   ├── profile.tsx        // Profile tab
│   └── more.tsx           // More tab
├── notifications.tsx      // Notifications screen
├── _layout.tsx           // Root layout
└── +not-found.tsx        // 404 screen

// Deep link examples:
// myapp://events/123
// myapp://chat/room-456
// myapp://auth/login
// myapp://notifications
```

---

## 6. API Integration Specifications

### 6.1 Base Configuration
```dart
class ApiConfig {
  static const String baseUrl = 'https://api.conference-app.com/v1';
  static const Duration timeout = Duration(seconds: 30);
  static const int retryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 1);
}

// Dio Configuration
class DioClient {
  late Dio _dio;
  
  DioClient() {
    _dio = Dio(BaseOptions(
      baseUrl: ApiConfig.baseUrl,
      connectTimeout: ApiConfig.timeout,
      receiveTimeout: ApiConfig.timeout,
      sendTimeout: ApiConfig.timeout,
    ));
    
    _dio.interceptors.addAll([
      AuthInterceptor(),
      LoggingInterceptor(),
      RetryInterceptor(),
      ErrorInterceptor(),
    ]);
  }
}
```

### 6.2 Required Endpoints

**Authentication:**
```dart
abstract class AuthApi {
  Future<ApiResponse<void>> sendVerificationCode(String email);
  Future<ApiResponse<AuthResponse>> verifyCode(String email, String code);
  Future<ApiResponse<AuthResponse>> refreshToken(String refreshToken);
  Future<ApiResponse<void>> logout();
}
```

**Events:**
```dart
abstract class EventsApi {
  Future<ApiResponse<List<Event>>> getEvents({
    int page = 1,
    int limit = 20,
    String? search,
    String? category,
    DateTime? startDate,
    DateTime? endDate,
  });
  
  Future<ApiResponse<Event>> getEventById(String id);
  Future<ApiResponse<void>> favoriteEvent(String id);
  Future<ApiResponse<void>> unfavoriteEvent(String id);
  Future<ApiResponse<List<String>>> getEventCategories();
}
```

**Chat:**
```dart
abstract class ChatApi {
  Future<ApiResponse<List<ChatRoom>>> getChatRooms();
  Future<ApiResponse<List<Message>>> getMessages(
    String roomId, {
    int page = 1,
    int limit = 50,
  });
  Future<ApiResponse<Message>> sendMessage(String roomId, SendMessageRequest request);
  Future<ApiResponse<void>> markMessageAsRead(String messageId);
}
```

### 6.3 Error Handling
```dart
@freezed
class ApiResponse<T> with _$ApiResponse<T> {
  const factory ApiResponse.success(T data) = _Success<T>;
  const factory ApiResponse.error(String message, int? statusCode) = _Error<T>;
}

class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final String? errorCode;
  
  const ApiException(this.message, {this.statusCode, this.errorCode});
}
```

---

## 7. Performance & Offline Requirements

### 7.1 Offline Functionality
```dart
class OfflineManager {
  static Future<void> cacheEssentialData() async {
    // Cache user profile
    // Cache favorited events
    // Cache recent chat messages
    // Cache app settings
  }
  
  static Future<void> syncWhenOnline() async {
    // Sync pending messages
    // Update cached events
    // Upload pending profile changes
    // Send analytics data
  }
}
```

### 7.2 Performance Optimizations
- Image caching with memory and disk cache
- Lazy loading for large lists
- Pagination for all data lists
- Debounced search functionality
- Connection pooling for network requests
- Background sync for non-critical data

---

## 8. Testing Strategy

### 8.1 Test Structure
```dart
// Unit Tests
group('AuthController', () {
  late AuthController controller;
  late MockAuthService mockService;
  
  setUp(() {
    mockService = MockAuthService();
    controller = AuthController(mockService);
  });
  
  test('should emit loading then success when login succeeds', () async {
    // Test implementation
  });
});

// Widget Tests
group('LoginPage', () {
  testWidgets('should show error when email is invalid', (tester) async {
    await tester.pumpWidget(const MaterialApp(home: LoginPage()));
    
    await tester.enterText(find.byType(TextFormField), 'invalid-email');
    await tester.tap(find.byType(ElevatedButton));
    await tester.pump();
    
    expect(find.text('Please enter a valid email'), findsOneWidget);
  });
});

// Integration Tests
group('Authentication Flow', () {
  testWidgets('should complete full auth flow', (tester) async {
    // Test complete authentication flow
  });
});
```

---

## 9. Deployment Configuration

### 9.1 Build Configurations
```dart
// build_config.dart
class BuildConfig {
  static const String environment = String.fromEnvironment('ENVIRONMENT', defaultValue: 'development');
  static const String apiBaseUrl = String.fromEnvironment('API_BASE_URL', defaultValue: 'https://dev-api.conference-app.com');
  static const bool enableLogging = bool.fromEnvironment('ENABLE_LOGGING', defaultValue: true);
  
  static bool get isDevelopment => environment == 'development';
  static bool get isProduction => environment == 'production';
}
```

### 9.2 Platform-Specific Configurations
```yaml
# android/app/build.gradle
android {
    compileSdkVersion 34
    buildToolsVersion "34.0.0"
    
    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }
    
    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}
```

---

## 10. LLM-Specific Implementation Prompts

### 10.1 Feature Development Prompts

**For Authentication:**
```
Create a complete Expo React Native authentication system with:
1. Email verification flow using Supabase Auth and Expo Router
2. Token management with Expo SecureStore
3. Auto-login functionality with session management
4. Proper error handling and loading states using React Query
5. Form validation with react-hook-form and zod
6. Biometric authentication option using Expo LocalAuthentication

Use Zustand for state management and follow the established TypeScript patterns.
```

**For Events:**
```
Implement an Expo React Native events management system with:
1. Event listing with search and filtering using FlatList
2. Event detail page with Expo Calendar integration
3. Favorite events functionality with optimistic updates
4. Offline caching using React Query and AsyncStorage
5. Pull-to-refresh with RefreshControl and pagination
6. Smooth transitions with React Native Reanimated

Follow the Event TypeScript interface structure provided.
```

**For Chat System:**
```
Build a real-time chat system with:
1. Supabase Realtime subscriptions for live messaging
2. Message pagination using FlatList with React Query
3. Image sharing with Expo ImagePicker and compression
4. Message reactions and replies functionality
5. Typing indicators and online status with Supabase presence
6. Push notifications using Expo Notifications

Use the ChatRoom and Message TypeScript interfaces provided.
```

### 10.2 Code Quality Checklist

When generating code, ensure:
- [ ] All async operations have proper error handling with try-catch blocks
- [ ] Loading states are implemented for all UI interactions
- [ ] TypeScript types are properly defined and used
- [ ] Accessibility props are included (accessibilityLabel, accessibilityRole, etc.)
- [ ] Performance optimizations are applied (useMemo, useCallback, FlatList optimization)
- [ ] Offline scenarios are considered with React Query caching
- [ ] Code follows React Native and Expo best practices
- [ ] ESLint and Prettier formatting is applied
- [ ] Comments explain complex business logic with JSDoc
- [ ] Error boundaries are implemented for critical components
- [ ] Unit tests are included for critical functionality

---

## 11. Success Metrics & Monitoring

### 10.1 Technical KPIs
- App crash rate < 0.1%
- ANR (Application Not Responding) rate < 0.05%
- App startup time < 3 seconds
- Memory usage < 150MB average
- Network error rate < 1%
- Offline functionality success rate > 95%

### 10.2 User Experience KPIs
- App store rating > 4.5 stars
- User retention rate > 80% (Day 7)
- Feature adoption rate > 60%
- User session duration > 10 minutes
- Chat engagement rate > 40%

---

## 11. Migration & Rollout Strategy

### 11.1 Phase 1: Core Features (Weeks 1-4)
- Authentication system
- Basic event listing
- User profile management
- Navigation structure

### 11.2 Phase 2: Advanced Features (Weeks 5-8)
- Real-time chat system
- Push notifications
- Offline functionality
- Advanced event features

### 11.3 Phase 3: Polish & Optimization (Weeks 9-12)
- Performance optimizations
- UI/UX improvements
- Comprehensive testing
- App store preparation

---

This enhanced PRD is specifically optimized for LLM-assisted development with clear implementation patterns, comprehensive code examples, and detailed technical specifications that can be used to generate high-quality Flutter code.